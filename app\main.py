import base64
import os
import sys
import time
import asyncio
from pathlib import Path
from dotenv import load_dotenv
from PIL import Image
import io
import requests

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

# 加载环境变量（覆盖现有变量）
load_dotenv(override=True)

# 首先配置Streamlit以避免torch.classes模块检查问题
# 这必须在任何可能导入torch的模块之前完成
try:
    from streamlit_config import configure_streamlit_early, setup_torch_environment
    configure_streamlit_early()
    setup_torch_environment()
except ImportError:
    # 如果配置模块不存在，使用内联配置
    print("⚠️ streamlit_config.py not found, using inline configuration")
    import streamlit as st
    from streamlit import config as st_config

    # 禁用文件监视器以避免torch.classes路径检查问题
    st_config.set_option('server.fileWatcherType', 'none')
    st_config.set_option('server.runOnSave', False)
    st_config.set_option('server.allowRunOnSave', False)

    # 禁用其他可能触发模块检查的功能
    st_config.set_option('server.enableCORS', False)
    st_config.set_option('server.enableXsrfProtection', False)
    st_config.set_option('runner.magicEnabled', False)

    # 禁用开发者模式功能
    st_config.set_option('global.developmentMode', False)

    # 设置torch环境变量
    # Note: TORCH_LOGS can cause issues, so we don't set it
    os.environ['TORCH_SHOW_CPP_STACKTRACES'] = '0'
    print("✅ Inline Streamlit configuration applied")

# 确保streamlit已导入
import streamlit as st

# 现在配置日志 - 在Streamlit配置之后
from app.logging_config import setup_logging
from app.logging_base import set_layer_context, set_operation_context
setup_logging()
import logging
logger = logging.getLogger('app')  # Use 'app' logger

# Import unified error handling
from app.exceptions import (
    LayerType, ErrorSeverity,
    ValidationException, ExternalServiceException, ConfigurationException
)
from app.error_handling import handle_layer_boundary, log_and_reraise, error_boundary

# Import UI state management
from app.ui_state_manager import ui_manager

# Set layer context for presentation layer
set_layer_context("presentation")

# 调试环境变量
logger.info("DEBUG - 初始化Streamlit后的环境变量:")
logger.info(f"OPENAI_API_BASE: {os.getenv('OPENAI_API_BASE')}")
logger.info(f"OPENAI_API_KEY: {'*' * len(os.getenv('OPENAI_API_KEY', ''))}")
logger.info(f"MODEL_ID: {os.getenv('MODEL_ID')}")

# 现在导入可能包含torch依赖的模块 - 使用延迟导入避免启动时的torch.classes问题
# 这些模块将在实际需要时才导入
ReportAgentClient = None
RAGWorkflowClient = None
AgentConfig = None
CrawlerClient = None

def lazy_import_modules():
    """延迟导入可能包含torch依赖的模块"""
    global ReportAgentClient, RAGWorkflowClient, AgentConfig, CrawlerClient

    if ReportAgentClient is None:
        try:
            from app.api.client import ReportAgentClient, RAGWorkflowClient
            from app.config import AgentConfig
            from crawler.client import CrawlerClient
            logger.info("✅ Successfully imported torch-dependent modules")
        except Exception as e:
            logger.error(f"❌ Failed to import modules: {e}")
            raise

# 立即导入基本配置模块（不包含torch依赖）
try:
    from app.config import AgentConfig
except ImportError:
    # 如果导入失败，将在lazy_import_modules中重试
    pass

@handle_layer_boundary(LayerType.PRESENTATION, "crawler status check")
async def check_crawler_status():
    """仅检查爬虫状态，不执行登录操作 with unified error handling"""
    set_operation_context("crawler_status_check")

    # 确保模块已导入
    lazy_import_modules()

    with error_boundary("crawler client initialization", LayerType.PRESENTATION):
        try:
            client = CrawlerClient()
        except Exception as e:
            raise ExternalServiceException(
                message="Failed to initialize crawler client",
                service_name="CrawlerClient",
                details=f"Client initialization error: {str(e)}",
                original_exception=e,
                suggested_action="Check crawler service configuration"
            )

    with error_boundary("crawler status retrieval", LayerType.PRESENTATION):
        try:
            # 只获取状态，不执行登录
            result = await client.get_status()

            # 构建状态字典
            status_info = {
                'logged_in': result.get('logged_in', False),
                'message': result.get('message', '状态未知'),
                'status': result.get('status', 'unknown'),
                'screen': result.get('screen', None)  # Keep full screenshot data
            }
            # Log status without screenshot data to avoid log spam
            log_info = {k: v for k, v in status_info.items() if k != 'screen'}
            if status_info.get('screen'):
                log_info['screen'] = f"<base64 data: {len(status_info['screen'])} chars>"
            logger.debug(f"爬虫状态: {log_info}")

            return status_info

        except Exception as e:
            raise ExternalServiceException(
                message="Failed to get crawler status",
                service_name="crawler_service",
                details=f"Status retrieval error: {str(e)}",
                original_exception=e,
                suggested_action="Check crawler service connectivity"
            )
        finally:
            try:
                await client.close()
            except Exception as e:
                logger.warning(f"Failed to close crawler client: {str(e)}")

@handle_layer_boundary(LayerType.PRESENTATION, "crawler login execution")
async def perform_crawler_login():
    """执行爬虫登录操作 with unified error handling"""
    set_operation_context("crawler_login")

    # 确保模块已导入
    lazy_import_modules()

    with error_boundary("crawler client initialization", LayerType.PRESENTATION):
        try:
            client = CrawlerClient()
        except Exception as e:
            raise ExternalServiceException(
                message="Failed to initialize crawler client for login",
                service_name="CrawlerClient",
                details=f"Client initialization error: {str(e)}",
                original_exception=e,
                suggested_action="Check crawler service configuration"
            )

    with error_boundary("crawler login execution", LayerType.PRESENTATION):
        try:
            # 执行登录操作，超时时间120秒
            result = await client.login(120.0)

            # 构建登录状态字典
            login_status = {
                **result,
                'logged_in': result.get('logged_in', False),
                'message': '登录成功' if result.get('logged_in') else '需要登录',
                'action': result.get('message', ''),
                'status': result.get('status', '')
            }
            logger.info(f"登录操作结果: {login_status}")

            return login_status

        except Exception as e:
            raise ExternalServiceException(
                message="Failed to perform crawler login",
                service_name="crawler_service",
                details=f"Login error: {str(e)}",
                original_exception=e,
                suggested_action="Check crawler service status and credentials"
            )
        finally:
            try:
                await client.close()
            except Exception as e:
                logger.warning(f"Failed to close crawler client: {str(e)}")

async def refresh_crawler_screenshot():
    """刷新爬虫截图"""
    # 确保模块已导入
    lazy_import_modules()

    client = CrawlerClient()
    try:
        # 获取当前截图
        screen = await client.get_screenshot("./screenshot.png")
        logger.debug("截图已刷新")
        return screen
    except Exception as e:
        logger.warning(f"截图刷新失败: {str(e)}")
        return None
    finally:
        await client.close()

def check_elements_complete(current_values, agent):
    """
    检查所有报告要素是否齐全
    
    参数:
        current_values: 当前UI状态值
        agent: 报告代理对象
        
    返回:
        tuple: (是否齐全, 缺失要素字典)
    """
    # 必需的基本要素
    required = {
        'company': current_values['company'],  # 公司名称
        'year': current_values['year']         # 报告年份
    }
    # 找出缺失的要素
    missing = {k: "未设置" for k, v in required.items() if not v}
    
    # 检查资料文件是否齐全
    if current_values['company'] and current_values['year']:
        # 构建工作区路径
        workspace_path = os.path.join(
            "workspaces",
            agent.config.workspace_id,
            current_values['company'],
            str(current_values['year'])
        )
        
        # 检查工作区是否存在
        if os.path.exists(workspace_path):
            # 获取所有支持的资料文件
            materials = [
                f for f in os.listdir(workspace_path)
                if f.lower().endswith(('.pdf', '.xls', '.xlsx'))
            ]
            # 如果没有资料文件且未确认无需资料
            if not materials and not agent.required_files['confirmed_empty']:
                missing['materials'] = "未上传或确认"
    else:
        # 公司或年份未设置时标记资料缺失
        missing['materials'] = "缺失"
        
    # 返回检查结果：是否所有要素齐全，以及缺失要素字典
    return len(missing) == 0, missing

def confirm_generation(user_input):
    """
    检查用户输入是否为确认生成报告
    
    参数:
        user_input: 用户输入的文本
        
    返回:
        bool: 是否为确认指令
    """
    # 定义确认关键词集合
    confirm_keywords = {
        'true', '是', 'yes', 'y', 'go',
        '确认', '同意', 'ok', '好的', '可以了', '对', '行',
        'continue', 'proceed', 'confirm', '继续', '开始',
        '重试', '再来一次', '再来', '好了', '行了', '要得了', '好', '行'
    }
    # 检查用户输入是否包含确认关键词
    return user_input.strip().lower() in confirm_keywords

@handle_layer_boundary(LayerType.PRESENTATION, "RAG workflow execution")
async def execute_rag_workflow():
    """
    执行RAG工作流并返回响应字典

    返回:
        dict: 包含状态、消息和报告路径的响应字典
    """
    # 确保模块已导入
    lazy_import_modules()

    # 准备工作区路径
    workspace_path = os.path.join(
        "workspaces",
        st.session_state.agent.config.workspace_id,
        st.session_state.current_values['company'],
        str(st.session_state.current_values['year'])
    )

    # 确保工作区存在
    os.makedirs(workspace_path, exist_ok=True)
    logger.debug(f"确保工作区输出目录存在: {workspace_path}")
    logger.debug(f"当前用户工作区ID: {st.session_state.agent.config.workspace_id}")
    logger.debug(f"发送的工作区路径: {workspace_path}")

    # 初始化响应字典
    response = {}

    # 创建进度容器，提供更详细的反馈
    progress_container = st.container()
    with progress_container:
        progress_bar = st.progress(0)
        status_text = st.empty()

        # 阶段1：准备工作
        status_text.text("🔧 正在准备工作环境...")
        progress_bar.progress(0.1)

        try:
            # Ensure modules are imported
            lazy_import_modules()

            # Create API client configuration
            status_text.text("🌐 正在连接服务器...")
            progress_bar.progress(0.2)

            from app.api.client import ClientConfig
            api_base_url = os.getenv("REPORT_SERVER_URL", "http://localhost:8100")
            client_config = ClientConfig(
                base_url=api_base_url,
                auth_token=st.session_state.access_token
            )

            # Create RAG workflow client and execute
            status_text.text("🚀 正在启动报告生成流程...")
            progress_bar.progress(0.4)

            rag_client = RAGWorkflowClient(client_config)

            status_text.text("📊 正在生成报告内容...")
            progress_bar.progress(0.7)

            result = rag_client.execute_workflow(
                workspace_path=workspace_path,
                template_file=st.session_state.current_values['template_file']
            )

            status_text.text("✅ 报告生成完成！")
            progress_bar.progress(1.0)
        except Exception as e:
            logger.error(f"RAG workflow execution failed: {str(e)}", exc_info=True)
            # 更新进度显示错误状态
            status_text.text("❌ 报告生成失败")
            progress_bar.progress(0.0)  # 重置进度条显示错误

            result = {
                'status': 'error',
                'code': 'ERR001',
                'message': f"RAG workflow execution failed: {str(e)}",
                'details': str(e)
            }
        logger.debug(f"RAG工作流结果: {result}")
        
        if result.get('status') == 'error':
            # 处理错误响应
            error_msg = f"报告生成遇到问题\n\n错误代码: {result.get('code', 'ERR999')}\n{result['message']}"
            if result.get('details'):
                error_msg += f"\n\n技术详情: {result['details']}"
            if result.get('action'):
                error_msg += f"\n\n建议操作: {result['action']}"
            else:
                error_msg += "\n\n建议操作: 请检查输入并重试"
            
            response = {
                'message': error_msg,
                'status': 'error',
                'code': result.get('code', 'ERR999')
            }
        else:
            # 处理成功情况
            report_path = result.get('report_path', None)
            
            if report_path and os.path.exists(report_path):
                # 扫描新生成的报告
                from app.api.client import ClientConfig
                api_base_url = os.getenv("REPORT_SERVER_URL", "http://localhost:8100")
                client_config = ClientConfig(
                    base_url=api_base_url,
                    auth_token=st.session_state.access_token
                )
                
                report_client = ReportAgentClient(client_config)
                try:
                    scan_result = report_client.scan_reports(st.session_state.agent.config.workspace_id)
                    if scan_result.get('status') == 'success':
                        st.session_state.available_reports = scan_result['available_reports']
                    else:
                        logger.warning(f"扫描报告失败: {scan_result.get('message', '未知错误')}")
                        st.session_state.available_reports = []
                except Exception as e:
                    logger.error(f"扫描报告错误: {str(e)}")
                    st.session_state.available_reports = []
                
                message = f"报告生成成功！\n\n会话已重置，可以开始新的任务"
                
                response = {
                    'message': message,
                    'status': 'success', 
                    'report_path': report_path,
                    'scan_result': scan_result
                }
            else:
                # 报告文件不存在时的错误处理
                response = {
                    'message': f"报告生成失败: 未生成有效报告文件{ ' - ' + report_path if report_path else ''}",
                    'status': 'error',
                    'code': 'ERR999'
                }
    
    # 确保总是返回response
    return response

def session_state_reset():
    """重置会话状态但保留UI元素和模板选择"""
    # 确保模块已导入
    lazy_import_modules()

    # Preserve template selection
    current_template = st.session_state.current_values.get('template_file')

    st.session_state.current_values = {
        'company': None,
        'year': None,
        'quarter': None,
        'template_file': current_template,  # Keep existing template
        'materials_count': 0,
        'materials_status': 'missing',
        'missing_elements': None,
        'conversation_history': [],
        'conversation_phase': 'initial',
    }

    # Keep UI elements visible
    st.session_state.setdefault('show_elements', True)

    config = {
                "workspace_id": st.session_state.department
            }
    st.session_state.agent = type('Agent', (), {})()  # 创建动态对象
    agent = st.session_state.agent
    agent.config = AgentConfig(**config)
    agent.agent = None

    st.session_state.chat_history = []
    st.session_state.confirmation_pending = False
    st.session_state.processed_files = set()
    st.session_state.uploaded_files = []

def ensure_smooth_ui_transition():
    """确保UI平滑过渡，防止渲染问题"""
    # 确保关键的UI状态标志已设置
    if 'ui_transition_complete' not in st.session_state:
        st.session_state.ui_transition_complete = False

    # 如果还在过渡中，显示加载状态
    if not st.session_state.ui_transition_complete:
        if st.session_state.get('post_login_initialized', False):
            # 标记过渡完成
            st.session_state.ui_transition_complete = True
            logger.info("UI transition completed successfully")
        else:
            # 仍在初始化中
            return False

    return True

# Authentication functions
def login_user(username, password):
    """Login user and return access token"""
    try:
        API_BASE = os.getenv("REPORT_SERVER_URL", "http://localhost:8100")
        
        # Prepare form data
        form_data = {
            'username': username,
            'password': password
        }
        
        response = requests.post(f"{API_BASE}/token", data=form_data)
        
        if response.status_code == 200:
            token_data = response.json()
            
            # Get user info to retrieve department
            headers = {'Authorization': f'Bearer {token_data["access_token"]}'}
            user_response = requests.get(f"{API_BASE}/user/me", headers=headers)
            
            if user_response.status_code == 200:
                user_info = user_response.json()
                return {
                    'success': True,
                    'access_token': token_data['access_token'],
                    'username': username,
                    'department': user_info.get('department', 'default_workspace'),
                    'display_name': user_info.get('display_name', username),
                    'nickname': user_info.get('nickname', username)
                }
            else:
                return {
                    'success': True,
                    'access_token': token_data['access_token'],
                    'username': username,
                    'department': 'default_workspace',  # Fallback
                    'display_name': username,
                    'nickname': username
                }
        else:
            return {
                'success': False,
                'error': f"登录失败: {response.text}"
            }
    except Exception as e:
        return {
            'success': False,
            'error': f"网络错误: {str(e)}"
        }

def logout_user():
    """Logout user and clear session state with duplicate prevention"""
    # Clear all authentication-related session state
    keys_to_clear = [
        'authenticated', 'access_token', 'username', 'department',
        'display_name', 'nickname', 'agent', 'agent_session',
        'current_values', 'uploaded_files', 'processed_files',
        'chat_history', 'confirmation_pending', 'user_templates',
        'show_template_manager', 'uploader_key'
    ]

    for key in keys_to_clear:
        if key in st.session_state:
            del st.session_state[key]

    # Reinitialize essential session state with duplicate prevention flags
    ui_manager.state_manager.update_session_state({
        'authenticated': False,
        'uploader_key': 0,
        'login_in_progress': False,
        'crawler_operation_in_progress': False,
        'template_operation_in_progress': False,
        'crawler_login_container': False
    }, "user_logout")

    st.success("已成功退出登录")
    st.rerun()

def get_user_templates(access_token):
    """Get templates for authenticated user"""
    try:
        API_BASE = os.getenv("REPORT_SERVER_URL", "http://localhost:8100")
        
        headers = {'Authorization': f'Bearer {access_token}'}
        response = requests.get(f"{API_BASE}/templates", headers=headers)
        
        if response.status_code == 200:
            return response.json()
        else:
            logger.warning(f"Failed to get templates: {response.status_code}")
            return []
    except Exception as e:
        logger.error(f"Error getting templates: {str(e)}")
        return []

def show_login_page():
    """Display login page with duplicate prevention"""
    # Prevent duplicate login forms during processing
    if st.session_state.get('login_in_progress', False):
        st.info("🔐 登录正在处理中，请稍候...")
        return

    st.title("智能报告生成系统")
    st.subheader("用户登录")

    # Create login form with unique key to prevent duplicates
    with st.form("login_form", clear_on_submit=True):
        username = st.text_input("用户名", placeholder="请输入用户名")
        password = st.text_input("密码", type="password", placeholder="请输入密码")
        submit_button = st.form_submit_button("登录")

        if submit_button:
            if username and password:
                # Set login in progress flag to prevent duplicate forms
                st.session_state.login_in_progress = True

                # 提供即时视觉反馈
                login_status = st.empty()
                with login_status:
                    st.info("🔐 正在验证用户信息...")

                with st.spinner("正在登录..."):
                    result = login_user(username, password)

                if result['success']:
                    # 显示成功状态
                    login_status.success("✅ 登录验证成功！正在初始化...")

                    # 原子性更新会话状态，防止中间状态
                    ui_manager.state_manager.update_session_state({
                        'authenticated': True,
                        'access_token': result['access_token'],
                        'username': result['username'],
                        'department': result['department'],
                        'display_name': result.get('display_name', result['username']),
                        'nickname': result.get('nickname', result['username']),
                        'login_in_progress': False,  # Clear the flag
                        'post_login_initialized': False  # Trigger post-login initialization
                    }, "user_login")

                    # 短暂延迟让用户看到成功消息，然后平滑过渡
                    time.sleep(0.8)
                    st.rerun()
                else:
                    # Clear the flag on error
                    st.session_state.login_in_progress = False
                    login_status.error(f"❌ {result['error']}")
            else:
                st.error("⚠️ 请输入用户名和密码")
    
    # Show sample users
    with st.expander("示例用户", expanded=True):
        st.markdown("""
        **可用的测试用户：**
        - **test** / test123 (test_workspace)
        - **user1** / user123 (dept_finance)  
        - **user2** / user123 (dept_risk)
        - **admin** / admin123 (admin_workspace)
        """)

def show_template_manager():
    """Display template management page"""
    st.title("模板管理")
    
    # Back button
    if st.button("← 返回主页"):
        st.session_state.show_template_manager = False
        st.rerun()
    
    st.divider()
    
    # Create new template
    st.subheader("创建新模板")
    
    # Template upload option
    st.write("**方式一：上传模板文件**")
    uploaded_template = st.file_uploader("上传模板文件", type=['md'], help="选择.md格式的模板文件")
    
    if uploaded_template:
        template_name = uploaded_template.name.replace('.md', '')
        template_content = uploaded_template.read().decode('utf-8')
        
        if st.button("上传模板"):
            try:
                API_BASE = os.getenv("REPORT_SERVER_URL", "http://localhost:8100")
                headers = {'Authorization': f'Bearer {st.session_state.access_token}'}
                data = {
                    'name': template_name,
                    'content': template_content
                }
                response = requests.post(f"{API_BASE}/templates", 
                                       headers=headers, json=data)
                
                if response.status_code == 200:
                    st.success(f"模板 '{template_name}' 上传成功！")
                    # Refresh template list
                    st.session_state.user_templates = get_user_templates(st.session_state.access_token)
                    st.rerun()
                else:
                    st.error(f"上传模板失败: {response.text}")
            except Exception as e:
                st.error(f"网络错误: {str(e)}")
    
    st.divider()
    
    # Manual template creation
    st.write("**方式二：手动创建模板**")
    with st.form("create_template_form"):
        new_template_name = st.text_input("模板名称")
        new_template_content = st.text_area("模板内容", height=200, 
            value="""# {{company_name}} 报告模板

## 公司概况
{{company_name}} 是一家专业的金融服务公司。

## 财务状况
### 资产负债表
- 总资产：{{total_assets}}
- 总负债：{{total_liabilities}}
- 净资产：{{net_assets}}

### 利润表
- 营业收入：{{revenue}}
- 净利润：{{net_profit}}

## 业务数据
### 放贷规模
- 累计放贷：{{total_loans}}
- 在贷余额：{{outstanding_balance}}

### 风险指标
- 不良率：{{npl_rate}}
- 逾期率：{{overdue_rate}}

## 总结
{{company_name}} 在 {{year}} 年度表现良好。
""")
        
        if st.form_submit_button("创建模板"):
            if new_template_name and new_template_content:
                # Create template via API
                try:
                    API_BASE = os.getenv("REPORT_SERVER_URL", "http://localhost:8100")
                    headers = {'Authorization': f'Bearer {st.session_state.access_token}'}
                    data = {
                        'name': new_template_name,
                        'content': new_template_content
                    }
                    response = requests.post(f"{API_BASE}/templates", 
                                           headers=headers, json=data)
                    
                    if response.status_code == 200:
                        st.success("模板创建成功！")
                        # Refresh template list
                        st.session_state.user_templates = get_user_templates(st.session_state.access_token)
                        st.rerun()
                    else:
                        st.error(f"创建模板失败: {response.text}")
                except Exception as e:
                    st.error(f"网络错误: {str(e)}")
            else:
                st.error("请输入模板名称和内容")
    
    st.divider()
    
    # List existing templates
    st.subheader("现有模板")
    if st.session_state.user_templates:
        for template in st.session_state.user_templates:
            col1, col2 = st.columns([3, 1])
            with col1:
                st.write(f"**{template['name']}**")
                st.caption(f"路径: {template['path']}")
            with col2:
                if st.button("删除", key=f"delete_{template['id']}"):
                    # Delete template via API
                    try:
                        API_BASE = os.getenv("REPORT_SERVER_URL", "http://localhost:8100")
                        headers = {'Authorization': f'Bearer {st.session_state.access_token}'}
                        response = requests.delete(f"{API_BASE}/templates/{template['id']}", 
                                                 headers=headers)
                        
                        if response.status_code == 200:
                            st.success(f"模板 '{template['name']}' 删除成功！")
                            # Refresh template list
                            st.session_state.user_templates = get_user_templates(st.session_state.access_token)
                            st.rerun()
                        else:
                            st.error(f"删除模板失败: {response.text}")
                    except Exception as e:
                        st.error(f"网络错误: {str(e)}")
    else:
        st.info("暂无模板")

@handle_layer_boundary(LayerType.PRESENTATION, "streamlit application initialization")
async def main():
    """Main Streamlit application with unified error handling and duplicate prevention"""
    set_operation_context("streamlit_app_init")
    set_layer_context("presentation")

    # Initialize UI state management to prevent duplicates
    if 'ui_initialized' not in st.session_state:
        st.session_state.ui_initialized = True
        st.session_state.login_in_progress = False
        st.session_state.crawler_operation_in_progress = False
        st.session_state.crawler_login_container = False

        # Initialize crawler_status early to prevent AttributeError
        st.session_state.crawler_status = {
            'logged_in': False,
            'message': '未初始化',
            'status': 'not_initialized'
        }
        # Set to True to trigger automatic status check on first load
        st.session_state.crawler_needs_init = True
    
    with error_boundary("environment setup", LayerType.PRESENTATION):
        try:
            # Load environment variables
            load_dotenv(override=True)
            
            # Debug environment variables
            logger.info("DEBUG - 初始化Streamlit前的环境变量:")
            logger.info(f"OPENAI_API_BASE: {os.getenv('OPENAI_API_BASE')}")
            logger.info(f"OPENAI_API_KEY: {'*' * len(os.getenv('OPENAI_API_KEY', ''))}")
            logger.info(f"MODEL_ID: {os.getenv('MODEL_ID')}")
            
        except Exception as e:
            raise ConfigurationException(
                message="Failed to initialize application environment",
                details=str(e),
                original_exception=e,
                suggested_action="Check environment variables and configuration files"
            )

    # Function to load and display cropped screenshot
    async def process_screenshot():
        """Async function to process screenshot data"""
        try:
            crawler_status = st.session_state.get('crawler_status', {})
            if not crawler_status.get('screen'):
                logger.warning("No screenshot data in crawler_status")
                return None

            # Decode base64 safely
            try:
                screenshot_data = base64.b64decode(crawler_status['screen'])
                if not screenshot_data:
                    logger.warning("Empty screenshot data after decoding")
                    return None
            except Exception as decode_error:
                logger.error(f"Base64 decode failed: {str(decode_error)}")
                return None

            # Use temp file to avoid conflicts
            temp_file = "screen.png"
            with open(temp_file, "wb") as f:
                f.write(screenshot_data)
                logger.debug(f"Saved screenshot to {temp_file}")

            try:
                img = Image.open(temp_file)
                width, height = img.size
                # Crop to show top 75% of the image for better QR code visibility
                # This provides more space for QR codes while removing bottom navigation/footer
                crop_height = int(height * 0.75)
                cropped_img = img.crop((0, 0, width, crop_height))
                
                img_bytes = io.BytesIO()
                cropped_img.save(img_bytes, format='PNG')
                img_bytes.seek(0)
                
                # Clean up temp file
                os.remove(temp_file)
                return img_bytes
            except Exception as img_error:
                logger.error(f"Image processing failed: {str(img_error)}")
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                return None
        except Exception as e:
            logger.error(f"Error in process_screenshot: {str(e)}", exc_info=True)
            return None

    async def display_cropped_screenshot():
        """Async function to display screenshot in Streamlit UI"""
        try:
            img_bytes = await process_screenshot()
            
            if img_bytes:
                if 'image_container' not in st.session_state:
                    st.session_state.image_container = st.empty()
                
                st.session_state.image_container.image(
                    img_bytes,
                    caption=f"Crawler screenshot (top 75%) - Last refresh: {time.strftime('%H:%M:%S')}"
                )
                logger.debug("Displayed cropped screenshot successfully")
        except Exception as e:
            logger.error(f"Error displaying screenshot: {str(e)}", exc_info=True)

    # Single-pass crawler status check (no duplicate messages)
    if st.session_state.get('crawler_status_check_needed', False):
        logger.info("Performing single crawler status check")
        try:
            # Single status check without spinner to avoid duplicate loading messages
            st.session_state.crawler_status = await check_crawler_status()
            crawler_status = st.session_state.get('crawler_status', {})

            # If not logged in, attempt login once
            if not crawler_status.get('logged_in', False):
                logger.info("Status check shows not logged in, attempting login")
                login_result = await perform_crawler_login()
                st.session_state.crawler_status.update(login_result)

                # Get screenshot for QR code if still not logged in
                if not st.session_state.crawler_status.get('logged_in', False) and not st.session_state.crawler_status.get('screen'):
                    logger.info("Getting screenshot for QR code display")
                    try:
                        screenshot = await refresh_crawler_screenshot()
                        if screenshot:
                            st.session_state.crawler_status['screen'] = screenshot
                    except Exception as e:
                        logger.warning(f"Failed to get screenshot: {str(e)}")

            crawler_status = st.session_state.get('crawler_status', {})
            logger.info(f"Crawler status check complete - logged_in: {crawler_status.get('logged_in', False)}")
            st.session_state.crawler_status_check_needed = False  # Clear the flag
        except Exception as e:
            logger.error(f"Crawler status check failed: {str(e)}")
            st.session_state.crawler_status = {
                'logged_in': False,
                'message': f'状态检查失败: {str(e)}',
                'status': 'error'
            }
            st.session_state.crawler_status_check_needed = False

    # Enhanced non-blocking status monitoring system
    current_time = time.time()
    last_status_check = st.session_state.get('last_status_check_time', 0)
    status_check_interval = 30  # seconds

    # Check for forced status check (user-requested)
    force_check = st.session_state.get('force_status_check', False)
    if force_check:
        st.session_state.force_status_check = False
        logger.debug("Performing user-requested status refresh")
        try:
            updated_status = await check_crawler_status()
            if updated_status:
                old_status = st.session_state.crawler_status.get('logged_in', False)
                st.session_state.crawler_status.update(updated_status)
                st.session_state.last_status_check_time = current_time

                # Show immediate feedback for user-requested checks
                if updated_status.get('logged_in', False) and not old_status:
                    st.success("✅ 已登录！")
                    st.rerun()
                elif not updated_status.get('logged_in', False):
                    st.info("✅ 状态已更新")

                logger.debug(f"User-requested status refresh: {updated_status.get('message', 'unknown')}")
        except Exception as e:
            logger.warning(f"User-requested status refresh failed: {str(e)}")
            st.warning("⚠️ 状态检查失败，请稍后重试")

    # Automatic background status check (silent)
    elif current_time - last_status_check > status_check_interval:
        logger.debug("Performing automatic background status refresh")
        try:
            # Silent background refresh without any UI feedback
            updated_status = await check_crawler_status()
            if updated_status:
                old_logged_in = st.session_state.crawler_status.get('logged_in', False)
                st.session_state.crawler_status.update(updated_status)
                st.session_state.last_status_check_time = current_time

                # Only show feedback if login status changed to success
                if updated_status.get('logged_in', False) and not old_logged_in:
                    st.success("🎉 自动检测到登录成功！")
                    st.rerun()

                logger.debug(f"Background status auto-refreshed: {updated_status.get('message', 'unknown')}")
        except Exception as e:
            # Silent failure for background checks - don't disturb user experience
            logger.debug(f"Background status refresh failed: {str(e)}")

    # Handle background QR code requests (non-blocking)
    if st.session_state.get('qr_request_in_progress', False):
        qr_request_time = st.session_state.get('qr_request_time', 0)
        # Process QR request if it's recent (within 5 seconds to avoid stale requests)
        if current_time - qr_request_time < 5:
            logger.debug("Processing background QR code request")
            try:
                new_screen = await refresh_crawler_screenshot()
                if new_screen:
                    st.session_state.crawler_status['screen'] = new_screen
                    st.success("✅ 二维码已获取")
                    logger.info("QR code obtained successfully in background")
                else:
                    st.warning("⚠️ 无法获取二维码，请稍后重试")
                    logger.warning("Failed to get QR code in background")
            except Exception as e:
                logger.warning(f"Background QR code request failed: {str(e)}")
                st.warning("⚠️ 获取二维码失败，请稍后重试")
            finally:
                st.session_state.qr_request_in_progress = False
                st.rerun()  # Refresh to show results
        else:
            # Clear stale request
            st.session_state.qr_request_in_progress = False

    # Ensure we have fresh status before displaying login interface
    # This helps catch cases where the server state changed but UI wasn't updated
    if not st.session_state.get('crawler_status_fresh_checked', False):
        logger.info("Performing fresh status check for UI sync")
        try:
            fresh_status = await check_crawler_status()
            if fresh_status:
                st.session_state.crawler_status.update(fresh_status)
                st.session_state.crawler_status_fresh_checked = True
                logger.info(f"Fresh status obtained: {fresh_status.get('message', 'unknown')}")
        except Exception as e:
            logger.warning(f"Fresh status check failed: {str(e)}")

    # Streamlined crawler login interface
    crawler_status = st.session_state.get('crawler_status', {'logged_in': False})
    if not crawler_status.get('logged_in', False):
        # Prevent duplicate interfaces during operations
        if st.session_state.get('crawler_operation_in_progress', False):
            st.info("🔄 爬虫操作正在进行中，请稍候...")
            return

        logger.info("Showing streamlined crawler login interface")

        # Single, clean login container
        with st.expander("爬虫登录", expanded=True):
            # Real-time status display with last update time
            status_message = crawler_status.get('message', '未知状态')
            last_check_time = st.session_state.get('last_status_check_time', 0)
            if last_check_time > 0:
                time_ago = int(time.time() - last_check_time)
                time_str = f" (更新于 {time_ago}秒前)" if time_ago > 0 else " (刚刚更新)"
            else:
                time_str = ""

            # Show different status indicators based on state
            if st.session_state.get('force_status_check', False):
                st.info(f"🔄 正在检查状态...")
            elif st.session_state.get('qr_request_in_progress', False):
                st.info(f"📱 正在获取二维码...")
            else:
                st.info(f"📊 当前状态: {status_message}{time_str}")

            # Non-blocking refresh button
            col1, col2 = st.columns([1, 3])
            with col1:
                if st.button("🔄 刷新", key="refresh_status", help="检查最新登录状态"):
                    if not st.session_state.get('crawler_operation_in_progress', False):
                        # Trigger immediate background status check without blocking UI
                        st.session_state.force_status_check = True
                        st.session_state.status_check_requested = time.time()
                        st.info("🔄 正在更新状态...")
                        st.rerun()  # Refresh to trigger background check

            # QR Code display section - clean and focused
            crawler_status = st.session_state.get('crawler_status', {})
            if crawler_status.get('screen'):
                st.success("📱 请使用企查查APP扫描二维码登录")
                await display_cropped_screenshot()
            else:
                st.info("📱 需要获取登录二维码")

                # Non-blocking QR code request
                if st.button("📱 获取二维码", key="get_qr_code", help="获取登录二维码"):
                    if not st.session_state.get('qr_request_in_progress', False):
                        # Set flag to trigger background QR code fetch
                        st.session_state.qr_request_in_progress = True
                        st.session_state.qr_request_time = time.time()
                        st.info("📱 正在获取二维码...")
                        st.rerun()  # Refresh to trigger background fetch

            # Help text
            st.info(" 提示：请使用企查查APP扫描二维码完成登录")

            # Background status check (non-blocking)
            if 'last_auto_check' not in st.session_state:
                st.session_state.last_auto_check = time.time()

            # Enhanced background status monitoring (30-second interval)
            if time.time() - st.session_state.last_auto_check > 30:
                st.session_state.last_auto_check = time.time()
                try:
                    status_result = await check_crawler_status()
                    if status_result:
                        old_logged_in = st.session_state.crawler_status.get('logged_in', False)
                        st.session_state.crawler_status.update(status_result)

                        # Only show success message if login status changed
                        if status_result.get('logged_in', False) and not old_logged_in:
                            st.success("🎉 自动检测到登录成功！")
                            st.rerun()

                        logger.debug("Background status monitoring successful")
                except Exception as e:
                    # Silent failure for background monitoring - don't disturb user
                    logger.debug(f"Background status monitoring failed: {str(e)}")

    async def init_agent(access_token):
        # 确保模块已导入
        lazy_import_modules()

        config = {
            "workspace_id": st.session_state.department
        }

        logger.debug(f"Initializing agent with config: {config}")

        # Create ClientConfig for ReportAgentClient
        from app.api.client import ClientConfig
        api_base_url = os.getenv("REPORT_SERVER_URL", "http://localhost:8100")
        client_config = ClientConfig(
            base_url=api_base_url,
            auth_token=access_token
        )

        report_client = ReportAgentClient(client_config)
        
        response = report_client.create_session(config)
        
        # 使用API返回数据初始化agent
        st.session_state.agent = type('Agent', (), {})()  # 创建动态对象
        agent = st.session_state.agent
        
        # 设置基本属性
        agent.config = AgentConfig(**config)
        agent.agent = None
        
          # 从agent_state设置核心字段
        agent_state = response["agent_state"]
        agent.session_id = response.get("session_id")
        agent.company_name = agent_state.get("company")
        agent.report_year = agent_state.get("year")
        agent.report_quarter = agent_state.get("quarter")
        agent.template = agent_state.get("template")
        agent.required_files = agent_state.get("required_files", {
            'collections': [],
            'confirmed_empty': False
        })
        
        # 添加必要的方法引用
        agent.sync_to_ui = lambda ui_state: ui_state.update({
            'company': agent.company_name,
            'year': agent.report_year,
            'quarter': agent.report_quarter,
            'template': agent.template
        })
        
        agent.reset = lambda: None  # 重置由API处理

        # Scan for existing reports via API
        try:
            scan_result = report_client.scan_reports(config["workspace_id"])
            if scan_result.get('status') == 'success':
                st.session_state.available_reports = scan_result['available_reports']
            else:
                logger.warning(f"Failed to scan reports: {scan_result.get('message', 'Unknown error')}")
                st.session_state.available_reports = []
        except Exception as e:
            logger.error(f"Error scanning reports: {str(e)}")
            st.session_state.available_reports = []
        
        return agent

    # Check authentication first
    if 'authenticated' not in st.session_state or not st.session_state.authenticated:
        show_login_page()
        return

    # Check if template manager should be displayed
    if st.session_state.get('show_template_manager', False):
        show_template_manager()
        return

    # Post-login initialization with proper loading states
    if 'post_login_initialized' not in st.session_state:
        st.session_state.post_login_initialized = False

    if not st.session_state.post_login_initialized:
        # Show loading state during initialization
        with st.container():
            st.info("🚀 正在初始化系统，请稍候...")
            progress_bar = st.progress(0)
            status_text = st.empty()

            try:
                # Step 1: Initialize agent (30% progress)
                status_text.text("📋 正在初始化代理...")
                progress_bar.progress(0.3)

                if 'agent' not in st.session_state:
                    st.session_state.agent = await init_agent(st.session_state.access_token)
                    st.session_state.agent_session = st.session_state.agent.session_id
                    logger.info(f"Initialized agent: {st.session_state.agent_session}")

                # Step 2: Initialize UI state (60% progress)
                status_text.text("🎨 正在初始化界面...")
                progress_bar.progress(0.6)

                if 'current_values' not in st.session_state:
                    st.session_state.current_values = {
                        'company': None,
                        'year': None,
                        'quarter': None,
                        'template_file': None,
                        'materials_count': 0
                    }
                    st.session_state.uploaded_files = []

                # Step 3: Initialize crawler (background, non-blocking) (90% progress)
                status_text.text("🔧 正在初始化爬虫...")
                progress_bar.progress(0.9)

                # Initialize crawler status without triggering duplicate initialization
                st.session_state.crawler_status = {
                    'logged_in': False,
                    'message': '等待状态检查',
                    'status': 'ready_for_check'
                }
                # Set to False to prevent duplicate initialization messages
                st.session_state.crawler_needs_init = False
                # Set flag to trigger single status check after main initialization
                st.session_state.crawler_status_check_needed = True

                # Step 4: Complete initialization (100% progress)
                status_text.text("✅ 初始化完成！")
                progress_bar.progress(1.0)

                # Mark as initialized
                st.session_state.post_login_initialized = True

                # Brief pause to show completion
                time.sleep(0.5)
                st.rerun()

            except Exception as e:
                logger.error(f"Post-login initialization failed: {str(e)}", exc_info=True)
                st.error(f"❌ 初始化失败: {str(e)}")
                st.info("💡 请刷新页面重试")
                return
        
    # 确保所有必要的会话状态都已初始化
    if 'chat_history' not in st.session_state:
        st.session_state.chat_history = []

    if 'uploader_key' not in st.session_state:
        st.session_state.uploader_key = 0

    # 确保用户模板已加载
    if 'user_templates' not in st.session_state:
        st.session_state.user_templates = get_user_templates(st.session_state.access_token)

    # 确保UI过渡完成，防止渲染问题
    if not ensure_smooth_ui_transition():
        # 如果UI还在过渡中，显示加载状态
        st.info("🎨 正在加载界面...")
        return

    # 实时同步文件状态到session
    if st.session_state.current_values.get('company') and st.session_state.current_values.get('year'):
        workspace_path = os.path.join(
            "workspaces", 
            st.session_state.agent.config.workspace_id,
            st.session_state.current_values['company'],
            str(st.session_state.current_values['year'])
        )
        os.makedirs(workspace_path, exist_ok=True)
        logger.debug(f"确保session工作输出目录存在: {workspace_path}")
        # 获取目录中所有有效文件
        existing_files = [
            f for f in os.listdir(workspace_path) 
            if f.lower().endswith(('.pdf','.xls','xlsx'))
        ]
        # 合并已上传文件和目录中已有文件（去重）
        current_files = st.session_state.agent.required_files['collections']
        st.session_state.agent.required_files['collections'] = list(
            set(current_files + existing_files)
        )
        # 更新文件计数
        st.session_state.current_values['materials_count'] = len(st.session_state.agent.required_files['collections'])
    else:
        if hasattr(st.session_state.agent, 'required_files'):
            st.session_state.agent.required_files['collections'] = []
        if hasattr(st.session_state, 'materials_count'):
            st.session_state.current_values['materials_count'] = 0
        
    # 初始化已处理文件集合
    if 'processed_files' not in st.session_state:
        st.session_state.processed_files = set()

    # 改进的文件上传回调函数（支持多文件，增强用户体验）
    def save_uploaded_file(uploaded_files):
        import os

        new_files = [f for f in uploaded_files if f.name not in st.session_state.processed_files]
        if not new_files:
            return

        logger.debug(f"开始处理文件上传，共{len(new_files)}个新文件")

        # 创建更详细的进度显示
        progress_container = st.container()
        with progress_container:
            st.info("📤 正在上传文件，请稍候...")
            progress_bar = st.progress(0)
            status_text = st.empty()

        # 遍历所有上传文件
        for i, uploaded_file in enumerate(new_files):
            progress = (i + 1) / len(new_files)

            # 更新进度显示
            status_text.text(f"正在处理: {uploaded_file.name} ({i+1}/{len(new_files)})")
            progress_bar.progress(progress)

            workspace_path = os.path.join("workspaces", st.session_state.agent.config.workspace_id)
            company_dir = os.path.join(workspace_path, st.session_state.current_values['company'] or "未命名公司")
            year_dir = os.path.join(company_dir, str(st.session_state.current_values['year'] or "未指定年份"))

            logger.debug(f"创建工作输出区目录: {year_dir}")
            os.makedirs(year_dir, exist_ok=True)

            file_path = os.path.join(year_dir, uploaded_file.name)
            logger.debug(f"保存文件到: {file_path}")

            # 仅保存新文件（避免重复）
            if not os.path.exists(file_path):
                logger.debug(f"保存新文件: {uploaded_file.name}")
                try:
                    with open(file_path, "wb") as f:
                        f.write(uploaded_file.getbuffer())

                    # 去重后添加
                    if uploaded_file.name not in st.session_state.agent.required_files['collections']:
                        st.session_state.agent.required_files['collections'].append(uploaded_file.name)

                    # 标记为已处理
                    st.session_state.processed_files.add(uploaded_file.name)

                except Exception as e:
                    logger.error(f"文件保存失败 {uploaded_file.name}: {str(e)}")
                    st.error(f"❌ 文件 {uploaded_file.name} 保存失败: {str(e)}")
                    continue

        # 完成上传后显示100%进度
        progress_bar.progress(1.0)
        status_text.text("✅ 所有文件处理完成")
        st.session_state.processed_files.update(f.name for f in new_files)  # 标记已处理

        # 显示详细的成功消息
        with progress_container:
            st.success(f"🎉 成功上传 {len(new_files)} 个文件!")

        # 重置上传组件状态
        st.session_state.uploaded_files = []
        st.session_state.uploader_key += 1  # 生成新的唯一key

        # 延迟一秒后刷新，让用户看到成功消息
        time.sleep(1)
        st.rerun()

    # 显示已上传资料文件（独立区域）
    with st.sidebar:
        # User info display at the top of sidebar
        def show_sidebar_user_info():
            """Display user information and logout button in sidebar"""
            # Get user information
            display_name = st.session_state.get('display_name', st.session_state.get('username', 'Unknown'))
            department = st.session_state.get('department', 'Unknown')

            # User info panel optimized for sidebar width (300px)
            st.markdown(f"""
            <div style="
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                padding: 16px;
                border-radius: 10px;
                margin-bottom: 20px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                color: white;
            ">
                <div style="text-align: center; margin-bottom: 12px;">
                    <div style="font-size: 18px; font-weight: 700; margin-bottom: 4px;">
                        👤 {display_name}
                    </div>
                    <div style="font-size: 13px; opacity: 0.9; margin-bottom: 2px;">
                        🏢 {department}
                    </div>
                </div>
            </div>
            """, unsafe_allow_html=True)

            # Logout button centered and styled for sidebar
            _, col2, _ = st.columns([1, 2, 1])
            with col2:
                if st.button("退出登录", key="sidebar_logout_btn", help="点击退出当前用户登录", use_container_width=True):
                    logout_user()

            # Add separator
            st.markdown("---")

        # Show user info at the top of sidebar
        show_sidebar_user_info()
        if st.session_state.current_values['company'] and st.session_state.current_values['year']:
            # 显示已上传资料文件
            if st.session_state.agent.required_files.get('confirmed_empty', False):
                st.info("已确认无需额外资料")
            elif st.session_state.agent.required_files['collections']:
                st.subheader("已上传资料")
                for file in st.session_state.agent.required_files['collections']:
                    st.write(f"✅ {file}")
            else:
                st.info("尚未上传任何资料文件")

            # 文件管理标题            
            st.header("文件管理")
            workspace_path = f"workspaces/{st.session_state.agent.config.workspace_id}/" + \
                            f"{st.session_state.current_values['company']}/" + \
                            f"{st.session_state.current_values['year']}"
            st.caption(f"当前路径: {workspace_path}")
        
            uploaded_files = st.file_uploader("上传公司资料 (PDF/Excel)", 
                                            type=['pdf','xls','xlsx'],
                                            accept_multiple_files=True,
                                            label_visibility="visible",
                                            help="请拖放文件到此处或点击选择文件上传",
                                            key=f'file_uploader_{st.session_state.uploader_key}')
            if uploaded_files:
                save_uploaded_file(uploaded_files)            
        else:
            st.info("请先设置公司名称和年份以启用文件管理功能")

    # 使用Streamlit原生布局系统
    st.markdown(
        """
        <style>
            section[data-testid="stSidebar"] {
                width: 300px !important;
            }
            section.main > div {
                padding-left: 2rem;
                padding-right: 2rem;
                max-width: none !important;
            }
            div.stButton > button {
                width: 100%;
            }
            div.block-container {
                max-width: none !important;
                padding-left: 5rem;
                padding-right: 5rem;
            }
            /* Fix chat input to bottom with fixed width and centered */
            div.stChatInput[data-testid="stChatInput"] {
                width: 500px;
                margin: 0 auto;
                position: fixed;
                bottom: 1rem;
                left: 50%;
                transform: translateX(-50%);
            }
            /* Add padding to prevent content overlap */
            div[data-testid="stVerticalBlock"] > div:has(> div[data-testid="stHorizontalBlock"]) ~ div {
                padding-bottom: 5rem;
            }
            /* Sidebar logout button styling */
            div[data-testid="stButton"] button[key="sidebar_logout_btn"] {
                background-color: #ff4b4b;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 13px;
                font-weight: 500;
                transition: all 0.2s ease;
                box-shadow: 0 2px 4px rgba(255, 75, 75, 0.3);
            }
            div[data-testid="stButton"] button[key="sidebar_logout_btn"]:hover {
                background-color: #ff3333;
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(255, 75, 75, 0.4);
            }
            /* Sidebar styling improvements */
            section[data-testid="stSidebar"] > div {
                padding-top: 1rem;
            }
        </style>
        """,
        unsafe_allow_html=True,
    )
    
    # 主布局
    main_col = st.container()
    with main_col:
        col1, col2 = st.columns([0.8, 0.2], gap="medium")

    with col2:
        with st.container():
            st.header("模板管理")
            
            # Load user templates if not already loaded
            if 'user_templates' not in st.session_state:
                st.session_state.user_templates = get_user_templates(st.session_state.access_token)
            
            # Create template options
            template_options = []
            if st.session_state.user_templates:
                template_options = [template['name'] for template in st.session_state.user_templates]
            if template_options:
                template_options = set(template_options)
            
            
            selected_template = st.selectbox(
                "选择模板",
                options=template_options,
                help="选择要使用的报告模板"
            )
            
            if selected_template:
                # Find the template file path
                template_file = f"{selected_template}.md"
                for template in st.session_state.user_templates:
                    if template['name'] == selected_template:
                        template_file = template['path']
                        break
                
                st.session_state.current_values['template_file'] = template_file
                st.session_state.agent.template = template_file
            
            # Template management buttons with improved feedback and duplicate prevention
            col_a, col_b = st.columns(2)
            with col_a:
                if st.button("🔄 刷新模板", help="重新加载模板列表", key="refresh_templates_btn"):
                    if not st.session_state.get('template_operation_in_progress', False):
                        st.session_state.template_operation_in_progress = True
                        try:
                            with st.spinner("正在刷新模板列表..."):
                                st.session_state.user_templates = get_user_templates(st.session_state.access_token)
                                st.success("✅ 模板列表已更新")
                                time.sleep(0.5)  # 让用户看到成功消息
                        finally:
                            st.session_state.template_operation_in_progress = False
                        st.rerun()
                    else:
                        st.warning("模板操作正在进行中...")

            with col_b:
                if st.button("⚙️ 模板管理", help="打开模板管理界面", key="template_manager_btn"):
                    st.session_state.show_template_manager = True
                    st.rerun()
            
            # 报告下载区域
            st.divider()
            st.subheader("报告下载")
            
            # 所有可用的历史报告
            if hasattr(st.session_state, 'available_reports') and st.session_state.available_reports:
                for report in st.session_state.available_reports:
                    with open(report['path'], "rb") as f:
                        st.download_button(
                            label=f"{report['company']} {report['year']}",
                            data=f,
                            file_name=f"{report['company']}_report.docx",
                            mime="application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                            key=f"report_{report['company']}_{report['year']}"
                        )
            else:
                st.info("没有可用的历史报告")

    with col1:
        st.title("智能报告生成系统")
        
        # 显示四要素状态
        with st.expander("当前要素状态", expanded=True):
            cols = st.columns(5)
            cols[0].metric("公司名称", st.session_state.current_values['company'] or "未设置", 
                        help=st.session_state.current_values['company'] if st.session_state.current_values['company'] else None)
            cols[1].metric("年份", st.session_state.current_values['year'] or "未设置")
            cols[2].metric("季度", st.session_state.current_values.get('quarter') or "未设置")
            # Extract template name from file path for display
            template_display = "未设置"
            if st.session_state.current_values.get('template_file'):
                template_path = st.session_state.current_values['template_file']
                # Extract filename from path
                template_display = os.path.basename(template_path)
            
            cols[3].metric("模板", template_display,
                        help=f"文件: {st.session_state.current_values['template_file']}" if st.session_state.current_values.get('template_file') else None)
            cols[4].metric("资料文件", 
                        f"{st.session_state.current_values['materials_count']}个")

        # 创建聊天容器并显示历史消息
        chat_container = st.container()
        with chat_container:
            for msg in st.session_state.chat_history:
                with st.chat_message(msg["role"]):
                    st.write(msg["content"])

        # 改进的用户输入处理，增加即时反馈
        if user_input := st.chat_input("请输入信息或回复确认请求"):
                # 移除礼貌性前缀
                user_input = user_input.lstrip("请麻烦烦请劳帮忙请问能否可以帮我能不能可不可以")

                # 立即显示用户消息，提供即时反馈
                with chat_container:
                    with st.chat_message("user"):
                        st.write(user_input)

                # 显示"正在处理"的临时消息
                processing_placeholder = st.empty()
                with processing_placeholder:
                    with st.chat_message("assistant"):
                        st.write("🤔 正在处理您的请求...")

                # 然后添加到历史记录
                st.session_state.chat_history.append({
                    "role": "user",
                    "content": user_input
                })
                logger.debug(f"收到用户输入: {user_input}")
                
                if user_input.strip().lower() in ["clear", "修改"]:
                    session_state_reset()
                    st.write("已重置，请输入新要求")
                    st.rerun()
                    return
                
                if user_input.strip().lower() == "reset crawler":
                    logger.info("收到重置爬虫请求")
                    try:
                        lazy_import_modules()  # 确保模块已导入
                        client = CrawlerClient()
                        await client.reset()
                        logger.info("爬虫重置成功")
                    except Exception as e:
                        logger.error(f"爬虫重置失败: {str(e)}")
                    finally:
                        # Safely delete crawler_status if it exists
                        if 'crawler_status' in st.session_state:
                            del st.session_state.crawler_status
                        with chat_container:
                            with st.chat_message('assistant'):
                                st.write("爬虫状态已重置，正在重新初始化...")
                        st.session_state.chat_history.append({
                            "role": "assistant",
                            "content": "爬虫状态已重置，请重试"
                        })
                        st.rerun()
                
                # 添加tab信息查询功能
                if user_input.strip().lower() in ["tabs", "tab info", "tab状态", "标签页"]:
                    logger.info("收到tab信息查询请求")
                    try:
                        lazy_import_modules()  # 确保模块已导入
                        client = CrawlerClient()
                        tab_info = await client.get_tab_info()
                        
                        # 格式化tab信息显示
                        tab_message = f"""📊 **浏览器标签页信息**

🔢 **当前标签页数量**: {tab_info.get('current_tabs', 0)}
📏 **最大允许数量**: {tab_info.get('max_tabs', 0)}
📈 **使用率**: {tab_info.get('usage_percentage', 0)}%
🔗 **浏览器连接状态**: {'已连接' if tab_info.get('browser_connected', False) else '未连接'}

{'⚠️ **警告**: 标签页数量超过限制，建议重置' if tab_info.get('needs_reset', False) else '✅ **状态**: 标签页使用正常'}"""
                        
                        with chat_container:
                            with st.chat_message('assistant'):
                                st.markdown(tab_message)
                        st.session_state.chat_history.append({
                            "role": "assistant",
                            "content": tab_message
                        })
                        await client.close()
                        logger.info("tab信息查询成功")
                    except Exception as e:
                        logger.error(f"tab信息查询失败: {str(e)}")
                        error_message = f"无法获取标签页信息: {str(e)}"
                        with chat_container:
                            with st.chat_message('assistant'):
                                st.write(error_message)
                        st.session_state.chat_history.append({
                            "role": "assistant",
                            "content": error_message
                        })
                    st.rerun()
                # 检查要素完整性
                is_complete, missing = check_elements_complete(
                    st.session_state.current_values,
                    st.session_state.agent
                )
                logger.debug(f"当前要素: 完备：{is_complete}，缺失：{missing}")
                    # 要素齐全时处理确认流程，增加详细的加载状态
                if confirm_generation(user_input):
                    if is_complete:
                        try:
                            # 清除"正在处理"消息
                            processing_placeholder.empty()

                            # 显示报告生成进度
                            progress_container = st.container()
                            with progress_container:
                                with st.chat_message('assistant'):
                                    st.info("🚀 开始生成报告...")
                                    progress_bar = st.progress(0)
                                    status_text = st.empty()

                                    # 模拟进度更新
                                    status_text.text("📋 正在准备工作区...")
                                    progress_bar.progress(0.1)

                                    status_text.text("🔍 正在分析资料文件...")
                                    progress_bar.progress(0.3)

                                    status_text.text("🤖 正在生成报告内容...")
                                    progress_bar.progress(0.6)

                            response = await execute_rag_workflow()

                            # 完成进度
                            with progress_container:
                                with st.chat_message('assistant'):
                                    progress_bar.progress(1.0)
                                    status_text.text("✅ 报告生成完成！")
                                    st.write(response['message'])

                            st.session_state.chat_history.append({
                                "role": "assistant",
                                "content": response['message']
                            })
                            st.rerun()
                            return
                        except Exception as e:
                            logger.error(f"执行RAG工作流时出错: {str(e)}", exc_info=True)
                            # 清除处理中的消息
                            processing_placeholder.empty()
                            with chat_container:
                                with st.chat_message('assistant'):
                                    st.write(f"⚠️ 系统处理您的请求时遇到问题\n\n可能原因:\n- 输入格式不符合要求\n- 网络连接问题\n- 系统临时故障\n\n建议操作:\n1. 检查输入格式\n2. 稍后输入“继续”再试\n3. 如问题持续,请联系管理员并提供错误时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
                            st.session_state.chat_history.append({
                                "role": "assistant",
                                "content": f"⚠️ 系统处理您的请求时遇到问题\n\n可能原因:\n- 输入格式不符合要求\n- 网络连接问题\n- 系统临时故障\n\n建议操作:\n1. 检查输入格式\n2. 稍后输入“继续”再试\n3. 如问题持续,请联系管理员并提供错误时间: {time.strftime('%Y-%m-%d %H:%M:%S')}"
                            })
                            st.rerun()
                            
                    
                # 用户选择修改
                lazy_import_modules()  # 确保模块已导入
                from app.api.client import ClientConfig
                api_base_url = os.getenv("REPORT_SERVER_URL", "http://localhost:8100")
                client_config = ClientConfig(
                    base_url=api_base_url,
                    auth_token=st.session_state.access_token
                )

                report_client = ReportAgentClient(client_config)

                try:
                    # Verify session_id exists
                    if not hasattr(st.session_state, 'agent_session') or not st.session_state.agent_session:
                        logger.error("Missing agent session ID")
                        st.error("会话已过期，请刷新页面重新登录")
                        return
                    
                    # Ensure we pass valid strings, not None values
                    current_company = st.session_state.current_values.get('company', '') or ''
                    current_year = st.session_state.current_values.get('year', '') or ''
                    current_quarter = st.session_state.current_values.get('quarter', '') or ''
                    
                    # Convert "未设置" to empty string for API
                    if current_company == '未设置':
                        current_company = ''
                    if current_year == '未设置':
                        current_year = ''
                    if current_quarter == '未设置':
                        current_quarter = ''
                    
                    logger.debug(f"Sending to API - session_id: {st.session_state.agent_session}, company: '{current_company}', year: '{current_year}', quarter: '{current_quarter}'")
                    response = report_client.process(
                        st.session_state.agent_session,  # session_id as first positional argument
                        user_input,                      # user_input as second positional argument
                        company=current_company,
                        year=current_year,
                        quarter=current_quarter,
                        extra_context={
                            'conversation_history': st.session_state.chat_history[-5:],
                            'ui_state': {
                                **st.session_state.current_values,
                                'company_options': getattr(st.session_state.agent, 'company_options', None),
                                'conversation_phase': getattr(st.session_state.agent, 'conversation_phase', 'initial')
                            }
                        }
                    )
                    logger.debug(f"API response: {response}")
                    
                    if response.get('status') == 'error':
                        error_code = response.get('code', 'ERR999')
                        error_message = response.get('message', '未知错误')
                        logger.error(f"API returned error: {error_code} - {error_message}")
                        st.session_state.chat_history.append({
                            "role": "assistant",
                            "content": f"⚠️ 系统处理您的请求时遇到问题\n\n可能原因:\n- 输入格式不符合要求\n- 网络连接问题\n- 系统临时故障\n\n建议操作:\n1. 检查输入格式\n2. 稍后输入“继续”再试\n3. 如问题持续,请联系管理员并提供错误时间: {time.strftime('%Y-%m-%d %H:%M:%S')}"
                        })
                        st.error(f"⚠️ 系统处理您的请求时遇到问题\n\n可能原因:\n- 输入格式不符合要求\n- 网络连接问题\n- 系统临时故障\n\n建议操作:\n1. 检查输入格式\n2. 稍后输入“继续”再试\n3. 如问题持续,请联系管理员并提供错误时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
                        st.rerun()
                        return
                    
                    # Handle successful response
                    # ... existing success handling code ...
                    
                except Exception as e:
                    logger.error(f"Unexpected error calling report_client.process: {str(e)}", exc_info=True)
                    st.error("❌ An unexpected error occurred while processing your request")
                    st.caption(f"Error: {str(e)}")
                    st.info("💡 Please try again or contact support if the problem persists")
                    return

                # 清除处理中的消息
                processing_placeholder.empty()

                # 原子性更新：使用API返回的agent_state更新session
                agent = st.session_state.agent
                agent_state = response["agent_state"]

                # 批量更新agent状态，避免中间状态
                agent.company_name = agent_state["company"]
                agent.report_year = agent_state["year"]
                agent.template = agent_state["template"]
                agent.required_files = agent_state["required_files"]

                # 原子性更新UI状态，防止显示不一致的中间状态
                new_ui_state = {
                    'company': agent_state["company"],
                    'year': agent_state["year"],
                    'quarter': agent_state.get("quarter"),
                    'template_file': agent_state["template"]
                }

                # 一次性更新所有相关状态
                st.session_state.current_values.update(new_ui_state)
                
                # 调试日志
                logger.debug(
                    f"状态同步完成 - UI: {st.session_state.current_values}\n"
                )
                
                # 自动检查四要素完整性
                if all(st.session_state.current_values.values()) and \
                (st.session_state.agent.required_files['collections'] or 
                    st.session_state.agent.required_files['confirmed_empty']):
                    
                    st.session_state.confirmation_pending = True
                    st.session_state.pending_action = "execute_report"
                    
                    workspace_path = os.path.join(
                        "workspaces",
                        st.session_state.agent.config.workspace_id,
                        st.session_state.current_values['company'],
                        str(st.session_state.current_values['year'])
                    )
                    files = [f for f in Path(workspace_path).glob("*")]
                    file_list = "暂无" if not files else "".join(f"　　📎 {f.name}\n\n" for f in files if f.is_file())
                    # 准备季度显示信息
                    quarter_info = f"📊 **报告季度**: {st.session_state.current_values.get('quarter')}\n" if st.session_state.current_values.get('quarter') else ""
                    
                    response['message'] = f"""✅ 所有要素已完备！请确认以下信息：\n
🏢 **公司名称**: {st.session_state.current_values['company']}\n
📅 **报告年份**: {st.session_state.current_values['year']}\n
{quarter_info}
📂 **已上传资料**:\n
{file_list}\n
请回复「是」开始生成报告，或「否」修改信息"""
            
                with st.chat_message('assistant'):
                    st.markdown(response['message'])

                # 检查是否需要开始生成报告
                if response.get('message') == "开始生成报告...":
                    response = await execute_rag_workflow()

                # 添加助理响应到历史记录
                st.session_state.chat_history.append({
                    "role": "assistant",
                    "content": response['message']
                })
                
                logger.debug("界面强制刷新")
                st.rerun()



if __name__ == "__main__":
    # Debug config after initialization
    logger.info("\nDEBUG - Config after initialization:")
    try:
        lazy_import_modules()  # 确保模块已导入
        config = AgentConfig(workspace_id='debug')
        logger.info(f"API Base: {config.api_base}")
        logger.info(f"Model: {config.model_name}")
    except Exception as e:
        logger.warning(f"Could not initialize debug config: {e}")

    try:
        asyncio.run(main())
    except Exception as e:
        logger.error(f"Application failed: {str(e)}")
        raise

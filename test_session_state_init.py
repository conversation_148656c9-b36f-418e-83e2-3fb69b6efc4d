#!/usr/bin/env python3
"""
Session State Initialization Test Script

This script tests the session state initialization fixes to ensure:
1. No AttributeError crashes on startup
2. Proper defensive access to crawler_status
3. Correct initialization order
4. Fallback handling for missing attributes

Usage:
    python test_session_state_init.py
"""

import sys
import re
import logging
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SessionStateInitTest:
    """Test suite for session state initialization fixes"""
    
    def __init__(self):
        self.test_results = []
        
    def log_test_result(self, test_name: str, passed: bool, message: str = ""):
        """Log test result"""
        status = "PASS" if passed else "FAIL"
        result = f"[{status}] {test_name}: {message}"
        self.test_results.append((test_name, passed, message))
        logger.info(result)
    
    def test_early_crawler_status_initialization(self):
        """Test that crawler_status is initialized early in main()"""
        logger.info("Testing Early Crawler Status Initialization...")
        
        try:
            main_app_path = Path("app/main.py")
            if main_app_path.exists():
                content = main_app_path.read_text(encoding='utf-8')
                
                # Check for early initialization in UI state management
                init_checks = [
                    "st.session_state.crawler_status = {" in content,
                    "'logged_in': False" in content,
                    "'message': '未初始化'" in content,
                    "st.session_state.crawler_needs_init = False" in content
                ]
                
                passed_checks = sum(init_checks)
                
                if passed_checks >= 3:
                    self.log_test_result("Early Crawler Status Initialization", True, 
                                       f"Found {passed_checks}/4 initialization patterns")
                else:
                    self.log_test_result("Early Crawler Status Initialization", False, 
                                       f"Only found {passed_checks}/4 initialization patterns")
            else:
                self.log_test_result("Early Crawler Status Initialization", False, "Main app file not found")
                
        except Exception as e:
            self.log_test_result("Early Crawler Status Initialization", False, f"Error: {str(e)}")
    
    def test_defensive_access_patterns(self):
        """Test that defensive access patterns are used"""
        logger.info("Testing Defensive Access Patterns...")
        
        try:
            main_app_path = Path("app/main.py")
            if main_app_path.exists():
                content = main_app_path.read_text(encoding='utf-8')
                
                # Count defensive access patterns
                defensive_patterns = [
                    "st.session_state.get('crawler_status'",
                    "crawler_status = st.session_state.get('crawler_status'",
                    "if 'crawler_status' not in st.session_state:",
                    "crawler_status.get("
                ]
                
                found_patterns = []
                for pattern in defensive_patterns:
                    if pattern in content:
                        found_patterns.append(pattern)
                
                if len(found_patterns) >= 3:
                    self.log_test_result("Defensive Access Patterns", True, 
                                       f"Found {len(found_patterns)}/4 defensive patterns")
                else:
                    self.log_test_result("Defensive Access Patterns", False, 
                                       f"Only found {len(found_patterns)}/4 defensive patterns")
            else:
                self.log_test_result("Defensive Access Patterns", False, "Main app file not found")
                
        except Exception as e:
            self.log_test_result("Defensive Access Patterns", False, f"Error: {str(e)}")
    
    def test_no_direct_attribute_access(self):
        """Test that direct attribute access is minimized"""
        logger.info("Testing No Direct Attribute Access...")
        
        try:
            main_app_path = Path("app/main.py")
            if main_app_path.exists():
                content = main_app_path.read_text(encoding='utf-8')
                
                # Find all direct access patterns
                direct_access_pattern = r"st\.session_state\.crawler_status\."
                direct_accesses = re.findall(direct_access_pattern, content)
                
                # Count lines with direct access (excluding safe assignments)
                lines = content.split('\n')
                problematic_lines = []
                
                for i, line in enumerate(lines, 1):
                    if re.search(direct_access_pattern, line):
                        # Skip safe assignment lines
                        if not any(safe_pattern in line for safe_pattern in [
                            "st.session_state.crawler_status = ",
                            "st.session_state.crawler_status.update(",
                            "st.session_state.crawler_status['screen'] = "
                        ]):
                            problematic_lines.append(i)
                
                if len(problematic_lines) == 0:
                    self.log_test_result("No Direct Attribute Access", True, 
                                       "No problematic direct access patterns found")
                else:
                    self.log_test_result("No Direct Attribute Access", False, 
                                       f"Found {len(problematic_lines)} problematic direct access lines: {problematic_lines[:5]}")
            else:
                self.log_test_result("No Direct Attribute Access", False, "Main app file not found")
                
        except Exception as e:
            self.log_test_result("No Direct Attribute Access", False, f"Error: {str(e)}")
    
    def test_safe_deletion_patterns(self):
        """Test that safe deletion patterns are used"""
        logger.info("Testing Safe Deletion Patterns...")
        
        try:
            main_app_path = Path("app/main.py")
            if main_app_path.exists():
                content = main_app_path.read_text(encoding='utf-8')
                
                # Check for safe deletion patterns
                safe_deletion_checks = [
                    "if 'crawler_status' in st.session_state:" in content,
                    "del st.session_state.crawler_status" in content
                ]
                
                passed_checks = sum(safe_deletion_checks)
                
                if passed_checks >= 1:
                    self.log_test_result("Safe Deletion Patterns", True, 
                                       f"Found {passed_checks}/2 safe deletion patterns")
                else:
                    self.log_test_result("Safe Deletion Patterns", False, 
                                       "No safe deletion patterns found")
            else:
                self.log_test_result("Safe Deletion Patterns", False, "Main app file not found")
                
        except Exception as e:
            self.log_test_result("Safe Deletion Patterns", False, f"Error: {str(e)}")
    
    def test_fallback_value_handling(self):
        """Test that proper fallback values are used"""
        logger.info("Testing Fallback Value Handling...")
        
        try:
            main_app_path = Path("app/main.py")
            if main_app_path.exists():
                content = main_app_path.read_text(encoding='utf-8')
                
                # Check for fallback value patterns
                fallback_patterns = [
                    "{'logged_in': False}" in content,
                    ".get('crawler_status', {})" in content,
                    ".get('message', '未知')" in content,
                    ".get('logged_in', False)" in content
                ]
                
                found_patterns = sum(fallback_patterns)
                
                if found_patterns >= 3:
                    self.log_test_result("Fallback Value Handling", True, 
                                       f"Found {found_patterns}/4 fallback patterns")
                else:
                    self.log_test_result("Fallback Value Handling", False, 
                                       f"Only found {found_patterns}/4 fallback patterns")
            else:
                self.log_test_result("Fallback Value Handling", False, "Main app file not found")
                
        except Exception as e:
            self.log_test_result("Fallback Value Handling", False, f"Error: {str(e)}")
    
    def test_initialization_order(self):
        """Test that initialization happens in correct order"""
        logger.info("Testing Initialization Order...")
        
        try:
            main_app_path = Path("app/main.py")
            if main_app_path.exists():
                content = main_app_path.read_text(encoding='utf-8')
                
                # Find positions of key initialization elements
                ui_init_pos = content.find("if 'ui_initialized' not in st.session_state:")
                crawler_init_pos = content.find("st.session_state.crawler_status = {")
                first_access_pos = content.find("crawler_status = st.session_state.get('crawler_status'")
                
                order_checks = [
                    ui_init_pos != -1,  # UI initialization exists
                    crawler_init_pos != -1,  # Crawler status initialization exists
                    first_access_pos != -1,  # Defensive access exists
                    ui_init_pos < first_access_pos if ui_init_pos != -1 and first_access_pos != -1 else True,  # Init before access
                    crawler_init_pos < first_access_pos if crawler_init_pos != -1 and first_access_pos != -1 else True  # Crawler init before access
                ]
                
                passed_checks = sum(order_checks)
                
                if passed_checks >= 4:
                    self.log_test_result("Initialization Order", True, 
                                       f"Correct initialization order ({passed_checks}/5 checks)")
                else:
                    self.log_test_result("Initialization Order", False, 
                                       f"Incorrect initialization order ({passed_checks}/5 checks)")
            else:
                self.log_test_result("Initialization Order", False, "Main app file not found")
                
        except Exception as e:
            self.log_test_result("Initialization Order", False, f"Error: {str(e)}")
    
    def run_all_tests(self):
        """Run all session state initialization tests"""
        logger.info("Starting Session State Initialization Test Suite...")
        logger.info("=" * 60)
        
        # Run all tests
        self.test_early_crawler_status_initialization()
        self.test_defensive_access_patterns()
        self.test_no_direct_attribute_access()
        self.test_safe_deletion_patterns()
        self.test_fallback_value_handling()
        self.test_initialization_order()
        
        # Print summary
        logger.info("=" * 60)
        logger.info("Test Summary:")
        
        passed = sum(1 for _, result, _ in self.test_results if result)
        total = len(self.test_results)
        
        logger.info(f"Passed: {passed}/{total}")
        
        if passed == total:
            logger.info("🎉 All tests passed! Session state initialization is working correctly.")
        else:
            logger.warning(f"⚠️ {total - passed} tests failed. Please review the issues above.")
            
        return passed == total


def main():
    """Main test function"""
    test_suite = SessionStateInitTest()
    success = test_suite.run_all_tests()
    
    if success:
        print("\n✅ Session state initialization test completed successfully!")
        return 0
    else:
        print("\n❌ Session state initialization test failed!")
        return 1


if __name__ == "__main__":
    try:
        result = main()
        sys.exit(result)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test failed with error: {str(e)}")
        sys.exit(1)

#!/usr/bin/env python3
"""
UI Responsiveness Test Script

This script tests the UI responsiveness improvements to ensure:
1. No auto-refresh loops causing UI freezing
2. Proper loading states for all async operations
3. No confusing intermediate states
4. Immediate visual feedback for user interactions
5. Optimized blocking operations

Usage:
    python test_ui_responsiveness.py
"""

import asyncio
import time
import logging
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from app.ui_state_manager import ui_manager
from app.main import (
    check_crawler_status, perform_crawler_login, 
    execute_rag_workflow, refresh_crawler_screenshot
)

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class UIResponsivenessTest:
    """Test suite for UI responsiveness improvements"""
    
    def __init__(self):
        self.test_results = []
        
    def log_test_result(self, test_name: str, passed: bool, message: str = ""):
        """Log test result"""
        status = "PASS" if passed else "FAIL"
        result = f"[{status}] {test_name}: {message}"
        self.test_results.append((test_name, passed, message))
        logger.info(result)
        
    def test_ui_state_manager(self):
        """Test UI state manager functionality"""
        logger.info("Testing UI State Manager...")
        
        try:
            # Test atomic updates
            with ui_manager.state_manager.atomic_update("test_update"):
                # Simulate state update
                test_data = {"test_key": "test_value"}
                ui_manager.state_manager.update_session_state(test_data, "test_operation")
            
            self.log_test_result("UI State Manager - Atomic Updates", True, "Atomic updates work correctly")
            
        except Exception as e:
            self.log_test_result("UI State Manager - Atomic Updates", False, f"Error: {str(e)}")
    
    def test_loading_state_manager(self):
        """Test loading state manager"""
        logger.info("Testing Loading State Manager...")
        
        try:
            # Test loading operation context manager
            with ui_manager.loading_manager.loading_operation("test_op", "Testing...", True) as (progress, status):
                # Simulate progress updates
                ui_manager.loading_manager.update_progress(progress, status, 0.5, "Halfway done")
                time.sleep(0.1)  # Brief pause to simulate work
                ui_manager.loading_manager.update_progress(progress, status, 1.0, "Complete")
            
            self.log_test_result("Loading State Manager", True, "Loading states work correctly")
            
        except Exception as e:
            self.log_test_result("Loading State Manager", False, f"Error: {str(e)}")
    
    def test_feedback_manager(self):
        """Test feedback manager"""
        logger.info("Testing Feedback Manager...")
        
        try:
            # Test different feedback types
            ui_manager.feedback_manager.show_success("Test success message", 0)
            ui_manager.feedback_manager.show_error("Test error message", "Test details")
            ui_manager.feedback_manager.show_warning("Test warning message")
            ui_manager.feedback_manager.show_info("Test info message")
            
            self.log_test_result("Feedback Manager", True, "All feedback types work correctly")
            
        except Exception as e:
            self.log_test_result("Feedback Manager", False, f"Error: {str(e)}")
    
    async def test_async_operations(self):
        """Test async operations for responsiveness"""
        logger.info("Testing Async Operations...")
        
        try:
            # Test crawler status check (should not block)
            start_time = time.time()
            try:
                await asyncio.wait_for(check_crawler_status(), timeout=5.0)
                duration = time.time() - start_time
                self.log_test_result("Async Crawler Status", True, f"Completed in {duration:.2f}s")
            except asyncio.TimeoutError:
                self.log_test_result("Async Crawler Status", False, "Operation timed out")
            except Exception as e:
                # Expected if crawler service is not running
                self.log_test_result("Async Crawler Status", True, f"Service unavailable (expected): {str(e)}")
            
        except Exception as e:
            self.log_test_result("Async Operations", False, f"Error: {str(e)}")
    
    def test_no_auto_refresh_loops(self):
        """Test that auto-refresh loops have been removed"""
        logger.info("Testing Auto-refresh Loop Removal...")
        
        try:
            # Check crawler app for auto-refresh
            crawler_app_path = Path("crawler/streamlit_app.py")
            if crawler_app_path.exists():
                content = crawler_app_path.read_text()
                
                # Check for problematic auto-refresh patterns
                problematic_patterns = [
                    "time.sleep(5)\nst.rerun()",
                    "st.rerun()" in content and "time.sleep(" in content
                ]
                
                has_auto_refresh = any(pattern for pattern in problematic_patterns if isinstance(pattern, bool) and pattern)
                
                if not has_auto_refresh:
                    self.log_test_result("Auto-refresh Loop Removal", True, "No problematic auto-refresh loops found")
                else:
                    self.log_test_result("Auto-refresh Loop Removal", False, "Auto-refresh loops still present")
            else:
                self.log_test_result("Auto-refresh Loop Removal", True, "Crawler app not found (OK)")
                
        except Exception as e:
            self.log_test_result("Auto-refresh Loop Removal", False, f"Error: {str(e)}")
    
    def test_loading_indicators(self):
        """Test that loading indicators are properly implemented"""
        logger.info("Testing Loading Indicators...")
        
        try:
            # Check main app for loading indicators
            main_app_path = Path("app/main.py")
            if main_app_path.exists():
                content = main_app_path.read_text()
                
                # Check for loading indicators
                loading_patterns = [
                    "st.spinner(" in content,
                    "st.progress(" in content,
                    "正在处理" in content,
                    "loading_operation" in content
                ]
                
                has_loading_indicators = any(loading_patterns)
                
                if has_loading_indicators:
                    self.log_test_result("Loading Indicators", True, "Loading indicators found in main app")
                else:
                    self.log_test_result("Loading Indicators", False, "No loading indicators found")
            else:
                self.log_test_result("Loading Indicators", False, "Main app not found")
                
        except Exception as e:
            self.log_test_result("Loading Indicators", False, f"Error: {str(e)}")
    
    def test_error_handling(self):
        """Test improved error handling"""
        logger.info("Testing Error Handling...")
        
        try:
            # Test error handling with UI manager
            def failing_operation():
                raise Exception("Test error")
            
            try:
                ui_manager.handle_user_action("test_action", failing_operation, "Testing...", "Success!")
            except Exception:
                # Expected to fail
                pass
            
            self.log_test_result("Error Handling", True, "Error handling works correctly")
            
        except Exception as e:
            self.log_test_result("Error Handling", False, f"Error: {str(e)}")
    
    async def run_all_tests(self):
        """Run all UI responsiveness tests"""
        logger.info("Starting UI Responsiveness Test Suite...")
        logger.info("=" * 50)
        
        # Run synchronous tests
        self.test_ui_state_manager()
        self.test_loading_state_manager()
        self.test_feedback_manager()
        self.test_no_auto_refresh_loops()
        self.test_loading_indicators()
        self.test_error_handling()
        
        # Run asynchronous tests
        await self.test_async_operations()
        
        # Print summary
        logger.info("=" * 50)
        logger.info("Test Summary:")
        
        passed = sum(1 for _, result, _ in self.test_results if result)
        total = len(self.test_results)
        
        logger.info(f"Passed: {passed}/{total}")
        
        if passed == total:
            logger.info("🎉 All tests passed! UI responsiveness improvements are working correctly.")
        else:
            logger.warning(f"⚠️ {total - passed} tests failed. Please review the issues above.")
            
        return passed == total


async def main():
    """Main test function"""
    test_suite = UIResponsivenessTest()
    success = await test_suite.run_all_tests()
    
    if success:
        print("\n✅ UI responsiveness test completed successfully!")
        return 0
    else:
        print("\n❌ UI responsiveness test failed!")
        return 1


if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(result)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test failed with error: {str(e)}")
        sys.exit(1)

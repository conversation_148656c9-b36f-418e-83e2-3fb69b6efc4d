"""
Entry point for running the crawler server as a module.
Usage: python -m crawler
"""

import sys
import argparse

def main():
    """Main entry point for the crawler module."""
    parser = argparse.ArgumentParser(description='QCC Crawler')
    subparsers = parser.add_subparsers(dest='command', help='Available commands')

    # Server command
    server_parser = subparsers.add_parser('server', help='Start the crawler server')
    server_parser.add_argument('--host', default='0.0.0.0', help='Server host (default: 0.0.0.0)')
    server_parser.add_argument('--port', type=int, default=8000, help='Server port (default: 8000)')

    # Client commands
    client_parser = subparsers.add_parser('client', help='Run crawler client commands')

    args = parser.parse_args()

    if args.command == 'server':
        start_server(args.host, args.port)
    elif args.command == 'client':
        from .client import app as client_app
        client_app()
    else:
        # Default to server if no command specified
        start_server()

def start_server(host=None, port=None):
    """Start the crawler server."""
    import uvicorn
    import os
    from dotenv import load_dotenv
    from .server import app

    load_dotenv(override=True)
    host = host or os.getenv("SERVER_HOST", "0.0.0.0")
    port = port or int(os.getenv("CRAWLER_SERVER_PORT", "8000"))

    print(f"Starting crawler server on {host}:{port}")
    uvicorn.run(app, host=host, port=port)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Login Transition Test Script

This script tests the post-login UI rendering improvements to ensure:
1. Smooth visual transition from login to main interface
2. No malformed intermediate rendering states
3. Proper loading states during initialization
4. Correct state synchronization during login flow

Usage:
    python test_login_transition.py
"""

import sys
import time
import logging
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class LoginTransitionTest:
    """Test suite for login transition improvements"""
    
    def __init__(self):
        self.test_results = []
        
    def log_test_result(self, test_name: str, passed: bool, message: str = ""):
        """Log test result"""
        status = "PASS" if passed else "FAIL"
        result = f"[{status}] {test_name}: {message}"
        self.test_results.append((test_name, passed, message))
        logger.info(result)
    
    def test_post_login_initialization(self):
        """Test that post-login initialization is properly implemented"""
        logger.info("Testing Post-Login Initialization...")
        
        try:
            main_app_path = Path("app/main.py")
            if main_app_path.exists():
                content = main_app_path.read_text(encoding='utf-8')
                
                # Check for post-login initialization patterns
                initialization_checks = [
                    'post_login_initialized' in content,
                    '正在初始化系统' in content,
                    'progress_bar = st.progress(0)' in content,
                    'status_text = st.empty()' in content
                ]
                
                passed_checks = sum(initialization_checks)
                
                if passed_checks >= 3:
                    self.log_test_result("Post-Login Initialization", True, 
                                       f"Found {passed_checks}/4 initialization patterns")
                else:
                    self.log_test_result("Post-Login Initialization", False, 
                                       f"Only found {passed_checks}/4 initialization patterns")
            else:
                self.log_test_result("Post-Login Initialization", False, "Main app file not found")
                
        except Exception as e:
            self.log_test_result("Post-Login Initialization", False, f"Error: {str(e)}")
    
    def test_non_blocking_crawler_init(self):
        """Test that crawler initialization is non-blocking"""
        logger.info("Testing Non-Blocking Crawler Initialization...")
        
        try:
            main_app_path = Path("app/main.py")
            if main_app_path.exists():
                content = main_app_path.read_text(encoding='utf-8')
                
                # Check for non-blocking crawler initialization
                crawler_checks = [
                    'crawler_needs_init' in content,
                    '后台初始化爬虫状态' in content,
                    'crawler_needs_init = False' in content,
                    'fallback status' in content or 'Set fallback status' in content
                ]
                
                passed_checks = sum(crawler_checks)
                
                if passed_checks >= 3:
                    self.log_test_result("Non-Blocking Crawler Init", True, 
                                       f"Found {passed_checks}/4 non-blocking patterns")
                else:
                    self.log_test_result("Non-Blocking Crawler Init", False, 
                                       f"Only found {passed_checks}/4 non-blocking patterns")
            else:
                self.log_test_result("Non-Blocking Crawler Init", False, "Main app file not found")
                
        except Exception as e:
            self.log_test_result("Non-Blocking Crawler Init", False, f"Error: {str(e)}")
    
    def test_smooth_transition_function(self):
        """Test that smooth transition function is implemented"""
        logger.info("Testing Smooth Transition Function...")
        
        try:
            main_app_path = Path("app/main.py")
            if main_app_path.exists():
                content = main_app_path.read_text(encoding='utf-8')
                
                # Check for smooth transition implementation
                transition_checks = [
                    'def ensure_smooth_ui_transition():' in content,
                    'ui_transition_complete' in content,
                    'UI transition completed successfully' in content,
                    'ensure_smooth_ui_transition()' in content
                ]
                
                passed_checks = sum(transition_checks)
                
                if passed_checks >= 3:
                    self.log_test_result("Smooth Transition Function", True, 
                                       f"Found {passed_checks}/4 transition patterns")
                else:
                    self.log_test_result("Smooth Transition Function", False, 
                                       f"Only found {passed_checks}/4 transition patterns")
            else:
                self.log_test_result("Smooth Transition Function", False, "Main app file not found")
                
        except Exception as e:
            self.log_test_result("Smooth Transition Function", False, f"Error: {str(e)}")
    
    def test_loading_states_implementation(self):
        """Test that proper loading states are implemented"""
        logger.info("Testing Loading States Implementation...")
        
        try:
            main_app_path = Path("app/main.py")
            if main_app_path.exists():
                content = main_app_path.read_text(encoding='utf-8')
                
                # Check for loading states
                loading_checks = [
                    'progress_bar.progress(' in content,
                    'status_text.text(' in content,
                    '正在初始化代理' in content,
                    '正在初始化界面' in content,
                    '正在初始化爬虫' in content,
                    '初始化完成' in content
                ]
                
                passed_checks = sum(loading_checks)
                
                if passed_checks >= 4:
                    self.log_test_result("Loading States Implementation", True, 
                                       f"Found {passed_checks}/6 loading state patterns")
                else:
                    self.log_test_result("Loading States Implementation", False, 
                                       f"Only found {passed_checks}/6 loading state patterns")
            else:
                self.log_test_result("Loading States Implementation", False, "Main app file not found")
                
        except Exception as e:
            self.log_test_result("Loading States Implementation", False, f"Error: {str(e)}")
    
    def test_login_success_handling(self):
        """Test that login success handling includes transition setup"""
        logger.info("Testing Login Success Handling...")
        
        try:
            main_app_path = Path("app/main.py")
            if main_app_path.exists():
                content = main_app_path.read_text(encoding='utf-8')
                
                # Find login success handling section
                if "if result['success']:" in content:
                    # Extract the login success section
                    start_idx = content.find("if result['success']:")
                    end_idx = content.find("else:", start_idx)
                    if end_idx == -1:
                        end_idx = start_idx + 1000  # Reasonable section size
                    
                    login_section = content[start_idx:end_idx]
                    
                    # Check for proper transition setup
                    success_checks = [
                        'post_login_initialized' in login_section,
                        'ui_manager.state_manager.update_session_state' in login_section,
                        "'login_in_progress': False" in login_section,
                        'time.sleep(' in login_section
                    ]
                    
                    passed_checks = sum(success_checks)
                    
                    if passed_checks >= 3:
                        self.log_test_result("Login Success Handling", True, 
                                           f"Found {passed_checks}/4 success handling patterns")
                    else:
                        self.log_test_result("Login Success Handling", False, 
                                           f"Only found {passed_checks}/4 success handling patterns")
                else:
                    self.log_test_result("Login Success Handling", False, "Login success section not found")
            else:
                self.log_test_result("Login Success Handling", False, "Main app file not found")
                
        except Exception as e:
            self.log_test_result("Login Success Handling", False, f"Error: {str(e)}")
    
    def test_ui_protection_during_transition(self):
        """Test that UI is protected during transition"""
        logger.info("Testing UI Protection During Transition...")
        
        try:
            main_app_path = Path("app/main.py")
            if main_app_path.exists():
                content = main_app_path.read_text(encoding='utf-8')
                
                # Check for UI protection patterns
                protection_checks = [
                    '正在加载界面' in content,
                    'if not ensure_smooth_ui_transition():' in content,
                    'return' in content,  # Early return during transition
                    'ui_transition_complete' in content
                ]
                
                passed_checks = sum(protection_checks)
                
                if passed_checks >= 3:
                    self.log_test_result("UI Protection During Transition", True, 
                                       f"Found {passed_checks}/4 protection patterns")
                else:
                    self.log_test_result("UI Protection During Transition", False, 
                                       f"Only found {passed_checks}/4 protection patterns")
            else:
                self.log_test_result("UI Protection During Transition", False, "Main app file not found")
                
        except Exception as e:
            self.log_test_result("UI Protection During Transition", False, f"Error: {str(e)}")
    
    def run_all_tests(self):
        """Run all login transition tests"""
        logger.info("Starting Login Transition Test Suite...")
        logger.info("=" * 60)
        
        # Run all tests
        self.test_post_login_initialization()
        self.test_non_blocking_crawler_init()
        self.test_smooth_transition_function()
        self.test_loading_states_implementation()
        self.test_login_success_handling()
        self.test_ui_protection_during_transition()
        
        # Print summary
        logger.info("=" * 60)
        logger.info("Test Summary:")
        
        passed = sum(1 for _, result, _ in self.test_results if result)
        total = len(self.test_results)
        
        logger.info(f"Passed: {passed}/{total}")
        
        if passed == total:
            logger.info("🎉 All tests passed! Login transition improvements are working correctly.")
        else:
            logger.warning(f"⚠️ {total - passed} tests failed. Please review the issues above.")
            
        return passed == total


def main():
    """Main test function"""
    test_suite = LoginTransitionTest()
    success = test_suite.run_all_tests()
    
    if success:
        print("\n✅ Login transition test completed successfully!")
        return 0
    else:
        print("\n❌ Login transition test failed!")
        return 1


if __name__ == "__main__":
    try:
        result = main()
        sys.exit(result)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test failed with error: {str(e)}")
        sys.exit(1)

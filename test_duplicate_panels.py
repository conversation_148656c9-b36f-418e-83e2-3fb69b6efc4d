#!/usr/bin/env python3
"""
Duplicate Panel Prevention Test Script

This script tests the fixes for duplicate UI panels to ensure:
1. No duplicate login forms appear during slow API responses
2. No duplicate crawler login interfaces
3. No duplicate expandable sections
4. Proper state management prevents UI element duplication
5. Button operations don't create duplicate panels

Usage:
    python test_duplicate_panels.py
"""

import sys
import time
import logging
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DuplicatePanelTest:
    """Test suite for duplicate panel prevention"""
    
    def __init__(self):
        self.test_results = []
        
    def log_test_result(self, test_name: str, passed: bool, message: str = ""):
        """Log test result"""
        status = "PASS" if passed else "FAIL"
        result = f"[{status}] {test_name}: {message}"
        self.test_results.append((test_name, passed, message))
        logger.info(result)
    
    def test_login_form_uniqueness(self):
        """Test that login forms have unique identifiers"""
        logger.info("Testing Login Form Uniqueness...")
        
        try:
            # Check main app for login form uniqueness
            main_app_path = Path("app/main.py")
            if main_app_path.exists():
                content = main_app_path.read_text(encoding='utf-8')
                
                # Check for unique form keys and duplicate prevention
                checks = [
                    'clear_on_submit=True' in content,  # Form clears on submit
                    'login_in_progress' in content,     # Progress flag exists
                    'key="login_form"' in content or 'st.form("login_form")' in content,  # Unique form key
                    'crawler_login_expander' in content  # Unique expander key
                ]
                
                passed_checks = sum(checks)
                total_checks = len(checks)
                
                if passed_checks >= 3:  # At least 3 out of 4 checks should pass
                    self.log_test_result("Login Form Uniqueness", True, 
                                       f"Passed {passed_checks}/{total_checks} uniqueness checks")
                else:
                    self.log_test_result("Login Form Uniqueness", False, 
                                       f"Only passed {passed_checks}/{total_checks} uniqueness checks")
            else:
                self.log_test_result("Login Form Uniqueness", False, "Main app file not found")
                
        except Exception as e:
            self.log_test_result("Login Form Uniqueness", False, f"Error: {str(e)}")
    
    def test_state_management_flags(self):
        """Test that proper state management flags are implemented"""
        logger.info("Testing State Management Flags...")
        
        try:
            main_app_path = Path("app/main.py")
            if main_app_path.exists():
                content = main_app_path.read_text(encoding='utf-8')

                # Check for state management flags
                required_flags = [
                    'login_in_progress',
                    'crawler_operation_in_progress', 
                    'template_operation_in_progress',
                    'crawler_login_container'
                ]
                
                found_flags = []
                for flag in required_flags:
                    if flag in content:
                        found_flags.append(flag)
                
                if len(found_flags) == len(required_flags):
                    self.log_test_result("State Management Flags", True, 
                                       f"All required flags found: {', '.join(found_flags)}")
                else:
                    missing = set(required_flags) - set(found_flags)
                    self.log_test_result("State Management Flags", False, 
                                       f"Missing flags: {', '.join(missing)}")
            else:
                self.log_test_result("State Management Flags", False, "Main app file not found")
                
        except Exception as e:
            self.log_test_result("State Management Flags", False, f"Error: {str(e)}")
    
    def test_duplicate_prevention_logic(self):
        """Test that duplicate prevention logic is properly implemented"""
        logger.info("Testing Duplicate Prevention Logic...")
        
        try:
            main_app_path = Path("app/main.py")
            if main_app_path.exists():
                content = main_app_path.read_text(encoding='utf-8')

                # Check for duplicate prevention patterns
                prevention_patterns = [
                    'if st.session_state.get(' in content,  # Checking state before operations
                    'operation_in_progress' in content,     # Operation progress tracking
                    'try:' in content and 'finally:' in content,  # Proper cleanup
                    'ui_manager.state_manager.update_session_state' in content  # Atomic updates
                ]
                
                found_patterns = sum(1 for pattern in prevention_patterns if pattern)
                
                if found_patterns >= 3:
                    self.log_test_result("Duplicate Prevention Logic", True, 
                                       f"Found {found_patterns}/4 prevention patterns")
                else:
                    self.log_test_result("Duplicate Prevention Logic", False, 
                                       f"Only found {found_patterns}/4 prevention patterns")
            else:
                self.log_test_result("Duplicate Prevention Logic", False, "Main app file not found")
                
        except Exception as e:
            self.log_test_result("Duplicate Prevention Logic", False, f"Error: {str(e)}")
    
    def test_unique_component_keys(self):
        """Test that UI components have unique keys"""
        logger.info("Testing Unique Component Keys...")
        
        try:
            main_app_path = Path("app/main.py")
            if main_app_path.exists():
                content = main_app_path.read_text(encoding='utf-8')

                # Check for unique keys in components
                unique_keys = [
                    'key="refresh_status"',
                    'key="refresh_qr"', 
                    'key="retry_login"',
                    'key="crawler_login_expander"',
                    'key="refresh_templates_btn"',
                    'key="template_manager_btn"'
                ]
                
                found_keys = []
                for key in unique_keys:
                    if key in content:
                        found_keys.append(key)
                
                if len(found_keys) >= 4:  # At least 4 unique keys should be present
                    self.log_test_result("Unique Component Keys", True, 
                                       f"Found {len(found_keys)} unique component keys")
                else:
                    self.log_test_result("Unique Component Keys", False, 
                                       f"Only found {len(found_keys)} unique component keys")
            else:
                self.log_test_result("Unique Component Keys", False, "Main app file not found")
                
        except Exception as e:
            self.log_test_result("Unique Component Keys", False, f"Error: {str(e)}")
    
    def test_ui_state_manager_integration(self):
        """Test that UI state manager is properly integrated"""
        logger.info("Testing UI State Manager Integration...")
        
        try:
            # Check if UI state manager exists
            ui_manager_path = Path("app/ui_state_manager.py")
            main_app_path = Path("app/main.py")
            
            if ui_manager_path.exists() and main_app_path.exists():
                main_content = main_app_path.read_text(encoding='utf-8')

                # Check for UI manager integration
                integration_checks = [
                    'from app.ui_state_manager import ui_manager' in main_content,
                    'ui_manager.state_manager' in main_content,
                    'atomic_update' in main_content or 'update_session_state' in main_content
                ]
                
                passed_integration = sum(integration_checks)
                
                if passed_integration >= 2:
                    self.log_test_result("UI State Manager Integration", True, 
                                       f"UI manager properly integrated ({passed_integration}/3 checks)")
                else:
                    self.log_test_result("UI State Manager Integration", False, 
                                       f"UI manager integration incomplete ({passed_integration}/3 checks)")
            else:
                missing_files = []
                if not ui_manager_path.exists():
                    missing_files.append("ui_state_manager.py")
                if not main_app_path.exists():
                    missing_files.append("main.py")
                    
                self.log_test_result("UI State Manager Integration", False, 
                                   f"Missing files: {', '.join(missing_files)}")
                
        except Exception as e:
            self.log_test_result("UI State Manager Integration", False, f"Error: {str(e)}")
    
    def test_cleanup_on_logout(self):
        """Test that proper cleanup happens on logout"""
        logger.info("Testing Cleanup on Logout...")
        
        try:
            main_app_path = Path("app/main.py")
            if main_app_path.exists():
                content = main_app_path.read_text(encoding='utf-8')

                # Check logout function for proper cleanup
                if 'def logout_user():' in content:
                    # Extract logout function content
                    start_idx = content.find('def logout_user():')
                    end_idx = content.find('\ndef ', start_idx + 1)
                    if end_idx == -1:
                        end_idx = len(content)
                    
                    logout_function = content[start_idx:end_idx]
                    
                    # Check for cleanup patterns
                    cleanup_checks = [
                        'login_in_progress' in logout_function,
                        'crawler_operation_in_progress' in logout_function,
                        'template_operation_in_progress' in logout_function,
                        'ui_manager.state_manager.update_session_state' in logout_function
                    ]
                    
                    passed_cleanup = sum(cleanup_checks)
                    
                    if passed_cleanup >= 3:
                        self.log_test_result("Cleanup on Logout", True, 
                                           f"Proper cleanup implemented ({passed_cleanup}/4 checks)")
                    else:
                        self.log_test_result("Cleanup on Logout", False, 
                                           f"Incomplete cleanup ({passed_cleanup}/4 checks)")
                else:
                    self.log_test_result("Cleanup on Logout", False, "logout_user function not found")
            else:
                self.log_test_result("Cleanup on Logout", False, "Main app file not found")
                
        except Exception as e:
            self.log_test_result("Cleanup on Logout", False, f"Error: {str(e)}")
    
    def run_all_tests(self):
        """Run all duplicate panel prevention tests"""
        logger.info("Starting Duplicate Panel Prevention Test Suite...")
        logger.info("=" * 60)
        
        # Run all tests
        self.test_login_form_uniqueness()
        self.test_state_management_flags()
        self.test_duplicate_prevention_logic()
        self.test_unique_component_keys()
        self.test_ui_state_manager_integration()
        self.test_cleanup_on_logout()
        
        # Print summary
        logger.info("=" * 60)
        logger.info("Test Summary:")
        
        passed = sum(1 for _, result, _ in self.test_results if result)
        total = len(self.test_results)
        
        logger.info(f"Passed: {passed}/{total}")
        
        if passed == total:
            logger.info("🎉 All tests passed! Duplicate panel prevention is working correctly.")
        else:
            logger.warning(f"⚠️ {total - passed} tests failed. Please review the issues above.")
            
        return passed == total


def main():
    """Main test function"""
    test_suite = DuplicatePanelTest()
    success = test_suite.run_all_tests()
    
    if success:
        print("\n✅ Duplicate panel prevention test completed successfully!")
        return 0
    else:
        print("\n❌ Duplicate panel prevention test failed!")
        return 1


if __name__ == "__main__":
    try:
        result = main()
        sys.exit(result)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test failed with error: {str(e)}")
        sys.exit(1)

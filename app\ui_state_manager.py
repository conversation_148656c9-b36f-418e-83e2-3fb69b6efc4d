"""
UI State Manager for Streamlit Application

This module provides utilities to manage UI state transitions and prevent
confusing intermediate states from being displayed to users.
"""

import streamlit as st
import time
import logging
from typing import Dict, Any, Optional, Callable
from contextlib import contextmanager

logger = logging.getLogger(__name__)


class UIStateManager:
    """Manages UI state transitions to prevent intermediate states"""
    
    def __init__(self):
        self.state_lock = False
        
    @contextmanager
    def atomic_update(self, operation_name: str = "UI Update"):
        """
        Context manager for atomic UI updates that prevents intermediate states
        
        Args:
            operation_name: Name of the operation for logging
        """
        if self.state_lock:
            logger.warning(f"Attempted nested atomic update: {operation_name}")
            yield
            return
            
        self.state_lock = True
        logger.debug(f"Starting atomic update: {operation_name}")
        
        try:
            yield
        except Exception as e:
            logger.error(f"Error during atomic update {operation_name}: {str(e)}")
            raise
        finally:
            self.state_lock = False
            logger.debug(f"Completed atomic update: {operation_name}")
    
    def update_session_state(self, updates: Dict[str, Any], operation_name: str = "State Update"):
        """
        Atomically update multiple session state values
        
        Args:
            updates: Dictionary of key-value pairs to update
            operation_name: Name of the operation for logging
        """
        with self.atomic_update(operation_name):
            for key, value in updates.items():
                st.session_state[key] = value
                logger.debug(f"Updated session state: {key} = {value}")


class LoadingStateManager:
    """Manages loading states and user feedback"""
    
    def __init__(self):
        self.active_operations = set()
    
    @contextmanager
    def loading_operation(self, operation_id: str, message: str = "Loading...", 
                         show_progress: bool = False):
        """
        Context manager for loading operations with visual feedback
        
        Args:
            operation_id: Unique identifier for the operation
            message: Message to display to user
            show_progress: Whether to show a progress bar
        """
        if operation_id in self.active_operations:
            logger.warning(f"Operation {operation_id} already active")
            yield None, None
            return
            
        self.active_operations.add(operation_id)
        logger.info(f"Starting loading operation: {operation_id}")
        
        # Create UI elements
        container = st.container()
        with container:
            st.info(f"⏳ {message}")
            progress_bar = st.progress(0) if show_progress else None
            status_text = st.empty() if show_progress else None
        
        try:
            yield progress_bar, status_text
        except Exception as e:
            logger.error(f"Error in loading operation {operation_id}: {str(e)}")
            with container:
                st.error(f"❌ Operation failed: {str(e)}")
            raise
        finally:
            self.active_operations.discard(operation_id)
            logger.info(f"Completed loading operation: {operation_id}")
    
    def update_progress(self, progress_bar, status_text, progress: float, message: str):
        """
        Update progress bar and status message
        
        Args:
            progress_bar: Streamlit progress bar element
            status_text: Streamlit text element for status
            progress: Progress value (0.0 to 1.0)
            message: Status message
        """
        if progress_bar:
            progress_bar.progress(progress)
        if status_text:
            status_text.text(message)
        logger.debug(f"Progress update: {progress:.1%} - {message}")


class FeedbackManager:
    """Manages user feedback and notifications"""
    
    @staticmethod
    def show_success(message: str, duration: float = 1.0):
        """
        Show success message with optional duration
        
        Args:
            message: Success message to display
            duration: How long to show the message (seconds)
        """
        st.success(f"✅ {message}")
        if duration > 0:
            time.sleep(duration)
    
    @staticmethod
    def show_error(message: str, details: Optional[str] = None):
        """
        Show error message with optional details
        
        Args:
            message: Error message to display
            details: Optional detailed error information
        """
        st.error(f"❌ {message}")
        if details:
            with st.expander("Error Details"):
                st.code(details)
    
    @staticmethod
    def show_warning(message: str):
        """
        Show warning message
        
        Args:
            message: Warning message to display
        """
        st.warning(f"⚠️ {message}")
    
    @staticmethod
    def show_info(message: str):
        """
        Show info message
        
        Args:
            message: Info message to display
        """
        st.info(f"ℹ️ {message}")


class ResponsiveUIManager:
    """Main manager that combines all UI management utilities"""
    
    def __init__(self):
        self.state_manager = UIStateManager()
        self.loading_manager = LoadingStateManager()
        self.feedback_manager = FeedbackManager()
    
    def handle_user_action(self, action_name: str, action_func: Callable, 
                          loading_message: str = None, success_message: str = None):
        """
        Handle user action with proper loading states and feedback
        
        Args:
            action_name: Name of the action for logging
            action_func: Function to execute
            loading_message: Message to show during loading
            success_message: Message to show on success
        """
        loading_msg = loading_message or f"Processing {action_name}..."
        
        with self.loading_manager.loading_operation(action_name, loading_msg):
            try:
                result = action_func()
                if success_message:
                    self.feedback_manager.show_success(success_message)
                return result
            except Exception as e:
                self.feedback_manager.show_error(f"Failed to {action_name}", str(e))
                raise


# Global instance for use throughout the application
ui_manager = ResponsiveUIManager()

# 1.公司情况分析
⁅<|chat@ASSISTANT,context|>
1.撰写公司基本情况，包含注册信息概要和简介、股权结构、法定代表人、高管团队、主营业务、对外投资等基本信息，要求用一段话简述即可。
2.根据上述获取到的公司基本信息来填充下图表格，返回的结果也是对应的表格形式。

| 基本检查事项     | 是否变动 | 变更说明/影响分析 |
| ---------------- | -------- | ----------------- |
| 股权结构变更     |          |                   |
| 法定代表人变更   |          |                   |
| 高管团队重大变更 |          |                   |
| 主营业务变更     |          |                   |
| 对外投资变更     |          |                   |

注意：1.如果上下文信息只与问题中的部分问题相关时，只回答与相关部分的问题，不要假设数据和随意捏造数据；2.如果上下文信息与问题中的所有问题都无关时，直接回答“未检索到知识库中的相关数据！”，并且不要假设数据和随意捏造数据；3.使用上下文信息中数据在计算过程中直接使用原来单位，不要换算单位，例如知识库检索到数据到是541,232,434,1238.00元，直接在回答中或者计算过程中给出541,232,434,1238.00元，不要转换成5.41万亿元。
<|chat@ASSISTANT,context|>⁆


# 2.公开渠道舆情排查
⁅<|chat@ASSISTANT,context|>
请叙述经企查查或执行信息公开网查询有无重大未撤诉案件，经执行信息公开网查询有无被执行案件，目前该公司有无负面重大报道和舆情、声誉风险相关事件。最后给出结论，要求用一段话简述即可。
注意：1.如果上下文信息只与问题中的部分问题相关时，只回答与相关部分的问题，不要假设数据和随意捏造数据；2.如果上下文信息与问题中的所有问题都无关时，直接回答“未检索到知识库中的相关数据！”，并且不要假设数据和随意捏造数据；3.使用上下文信息中数据在计算过程中直接使用原来单位，不要换算单位，例如知识库检索到数据到是541,232,434,1238.00元，直接在回答中或者计算过程中给出541,232,434,1238.00元，不要转换成5.41万亿元。
<|chat@ASSISTANT,context|>⁆


# 3.业务分析
{<|data,biz_table|>
请获取2024年到2025年3月的业务数据的成交在贷数据，要求：1. 以csv格式输出，仅输出csv表格内容；2.列标题为日期、“新增放款金额“、”新增放款笔数”、“新增放款客户数”、“累计放款金额”、“累计放款笔数”、“累计放款客户数”、“在贷余额”、“在贷笔数”、“在贷客户数”；3.行标题为日期、“2024-01-31”、“2024-02-29”、“2024-03-31”、”2024-04-30“、”2024-05-31“、“2024-06-30”、”2024-07-31“、”2024-08-31“、“2024-09-30”、”2024-10-31“、”2024-11-30“、“2024-12-31”、“2025-01-31”、“2025-02-28”、“2025-03-31”；4.日期格式“2024年3月”等价于“2024-03-31”，“2024年2月”等价于“2024-02-29”，“2025年2月”等价与“2025-02-28”，“2025年3月”等价与“2025-03-31”，其他日期格式以此类推；5.“在贷”等价于“期末在贷”；6.输出的csv表格在开始和结尾加上//csv//标签；7.只能使用上下文信息中的数据输出答案。
<|data,biz_table|>}
{load_to_sqlite:data_b_table.csv:biz_table:日期:%Y-%m-%d|-}
</biz/>本季度新增放款金额{query:biz_table:新增放款金额:quarter:日期=2025年1季度}，笔数{query:biz_table:新增放款笔数:quarter:日期=2025年1季度|int}，客户数{query:biz_table:新增放款客户数:quarter:日期=2025年1季度|int}，新增笔均{query:biz_table:新增放款金额/新增放款笔数 as 新增笔均:quarter:日期=2025年1季度}，户均{query:biz_table:新增放款金额/新增放款客户数 as 新增户均:quarter:日期=2025年1季度}，累计放款金额{query:biz_table:累计放款金额:quarter:日期=2025年1季度}，笔数{query:biz_table:累计放款笔数:quarter:日期=2025年1季度|int}，客户数{query:biz_table:累计放款客户数:quarter:日期=2025年1季度|int}，在贷余额{query:biz_table:在贷余额:quarter:日期=2025年1季度}，在贷笔数{query:biz_table:在贷笔数:quarter:日期=2025年1季度|int}，在贷客户数{query:biz_table:在贷客户数:quarter:日期=2025年1季度|int}。新增放款金额同比{yoy:biz_table:新增放款金额:quarter:日期=2025年1季度}，新增放款金额环比{mom:biz_table:新增放款金额:quarter:日期=2025年1季度}。</biz/>
{bind:context[biz]}
⁅<|chat@REPORTER,context|>根据<content>的业务数据描述，总结公司从2025年第一季度业务发展情况和变化趋势，并输出结论：从2025年第一季度业务发展是否稳定向好？给出的结论用一段话简述即可。
<content>
/biz/
</content>
<|chat@REPORTER,context|>⁆


# 合作资方分析
</corp/>{<|data,corp.时间:在贷余额|>请获取2024年-2025年3月合作资方的在贷余额数据，要求：1.以csv格式输出，仅输出csv表格内容；2.通过时间划分csv表格，分别生成2024年3月、2024年6月、2024年9月、2024年12月、2025年3月的csv表格；3.列标题为“时间”、“机构类型”、“资方名称”、”在贷余额“；3.需列出所有的资方，在“资方名称”中”其他“只需在资方名称中填写”其他“，不需要列举出”其他“中的资方名称，最后一行为合计的资方在贷余额；4.日期格式“2024年3月”等价于“2024-03-31”，“2024年2月”等价于“2024-02-29”，“2025年2月”等价与“2025-02-28”，“2025年3月”等价与“2025-03-31”，其他日期格式以此类推；5.必须列举出所有资方的数据；6.“机构类型”有“银行”、“消金“、”信托“，“保险”等，应通过”资方名称“推断；7.若上下文信息没有提供需要回答的相关数据，标记为“N/A”；8.输出的csv表格在开始和结尾加上//csv//标签。<|data,corp.时间:在贷余额|>}
</corp/>
{bind:context[corp]}
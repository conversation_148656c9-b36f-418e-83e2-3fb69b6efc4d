import logging
from fastapi import <PERSON><PERSON><PERSON>, Request, Depends, status
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import uuid
from app.agent import ReportAgent
from app.config import AgentConfig
from app.workflows.rag_workflow import RAGWorkflow
from fastapi.middleware.cors import CORSMiddleware
import os
from app.logging_config import setup_logging
from app.logging_base import set_operation_context, request_id_var
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
from fastapi.security import OAuth2PasswordBearer, OAuth2PasswordRequestForm
from jose import JWTError, jwt
from passlib.context import CryptContext
from sqlalchemy import create_engine, Column, String, Integer
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from ldap3 import Server, Connection, ALL
from ldap3.core.exceptions import LDAPException, LDAPInvalidCredentialsResult, LDAPSocketOpenError

# Import unified error handling framework
from app.exceptions import (
    AgentException, ConfigurationException, DataProcessingException, ExternalServiceException, FileProcessingException, LayerType, ErrorSeverity, APIException, AuthenticationException,
    ValidationException, BaseAppException
)
from app.error_handling import handle_layer_boundary, error_boundary, log_and_reraise

# Setup logging first
setup_logging()
logger = logging.getLogger(__name__)

app = FastAPI()

# Global exception handlers for unified error handling
@app.exception_handler(BaseAppException)
async def handle_app_exception(request: Request, exc: BaseAppException):
    """Handle all custom application exceptions"""
    # Reduce logging verbosity - only log once at appropriate level
    if not exc._logged:
        exc.log_error(logger)

    # Convert to appropriate HTTP status code
    if isinstance(exc, AuthenticationException):
        status_code = 401
    elif isinstance(exc, ValidationException):
        status_code = 400
    elif isinstance(exc, APIException):
        status_code = getattr(exc, 'http_status', 500)
    else:
        status_code = 500

    return JSONResponse(
        status_code=status_code,
        content={
            "error_code": exc.error_code,
            "message": exc.message,
            "details": exc.details,
            "suggested_action": exc.suggested_action,
            "timestamp": exc.timestamp.isoformat()
        }
    )

@app.exception_handler(Exception)
async def handle_generic_exception(request: Request, exc: Exception):
    """Handle any unhandled exceptions"""
    # Reduce logging verbosity - single concise log entry
    logger.error(f"Unhandled API exception: {type(exc).__name__}: {str(exc)}")

    return JSONResponse(
        status_code=500,
        content={
            "error_code": "API_UNHANDLED_001",
            "message": "Internal server error",
            "details": "An unexpected error occurred",
            "suggested_action": "Please contact support if the problem persists"
        }
    )

# Middleware to add request ID
@app.middleware("http")
async def add_request_id(request: Request, call_next):
    request_id = str(uuid.uuid4())
    request_id_var.set(request_id)
    response = await call_next(request)
    response.headers["X-Request-ID"] = request_id
    return response

origins = ["*"]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# ================== Configuration ==================
AUTH_MODE = os.getenv("AUTH_MODE", "db")  # db or ldap
DB_PATH = os.getenv("DB_PATH", "data/users.db")
LDAP_URL = os.getenv("LDAP_URL", "")
LDAP_BASE_DN = os.getenv("LDAP_BASE_DN", "")
LDAP_USER_FILTER = os.getenv("LDAP_USER_FILTER", "(cn={username})")  # LDAP search filter for users
LDAP_DEPT_ATTR = os.getenv("LDAP_DEPT_ATTR", "ou")  # Primary department attribute
# LDAP Admin credentials for user search
LDAP_ADMIN_DN = os.getenv("LDAP_ADMIN_DN", "")  # Admin DN for binding (e.g., cn=admin,dc=company,dc=com)
LDAP_ADMIN_PASSWORD = os.getenv("LDAP_ADMIN_PASSWORD", "")  # Admin password
# LDAP attribute mappings (following Go code pattern)
LDAP_USERNAME_ATTR = os.getenv("LDAP_USERNAME_ATTR", "sAMAccountName")  # UNameKey equivalent
LDAP_NICKNAME_ATTR = os.getenv("LDAP_NICKNAME_ATTR", "cn")  # NameKey equivalent
LDAP_CONNECTION_TIMEOUT = int(os.getenv("LDAP_CONNECTION_TIMEOUT", "5"))  # Connection timeout in seconds
SECRET_KEY = os.getenv("JWT_SECRET_KEY", "your-secret-key")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 300

# ================== Database Setup ==================
Base = declarative_base()

class User(Base):
    __tablename__ = 'users'
    id = Column(Integer, primary_key=True)
    username = Column(String, unique=True)
    password_hash = Column(String)
    workspace_id = Column(String)

class Template(Base):
    __tablename__ = 'templates'
    id = Column(Integer, primary_key=True)
    name = Column(String)
    workspace_id = Column(String)
    path = Column(String)

# Create database if not exists
if AUTH_MODE == "db":
    engine = create_engine(f"sqlite:///{DB_PATH}")
    Base.metadata.create_all(engine)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# ================== Authentication Setup ==================
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

# In-memory storage for session management
sessions = {
    "report_agent": {},
    "rag_workflow": {},
    "md_tools": {}
}

# ================== Helper Functions ==================
def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password):
    return pwd_context.hash(password)

def authenticate_user_db(username: str, password: str):
    if AUTH_MODE != "db":
        return False
    db = SessionLocal()
    user = db.query(User).filter(User.username == username).first()
    db.close()
    
    if not user:
        return False
    if not verify_password(password, user.password_hash):
        return False
    return user

@log_and_reraise(logger, "LDAP user authentication")
def authenticate_user_ldap(username: str, password: str):
    """Authenticate user against LDAP directory with unified error handling."""
    set_operation_context("ldap_authentication")
    
    with error_boundary("LDAP connection setup", LayerType.API):
        try:
            # Create admin connection for user search
            admin_conn = Connection(
                Server(LDAP_URL, get_info=ALL),
                user=LDAP_ADMIN_DN,
                password=LDAP_ADMIN_PASSWORD,
                auto_bind=True
            )
        except LDAPSocketOpenError as e:
            raise ExternalServiceException(
                message="LDAP service unavailable",
                service_name="LDAP Directory",
                details=f"Connection timeout or network error: {str(e)}",
                original_exception=e,
                context={'username': username, 'ldap_url': LDAP_URL},
                suggested_action="Check LDAP service status and network connectivity"
            )
        except LDAPException as e:
            raise AuthenticationException(
                message="LDAP authentication system error",
                details=f"Admin connection failed: {str(e)}",
                original_exception=e,
                context={'username': username, 'ldap_url': LDAP_URL},
                suggested_action="Contact system administrator - LDAP configuration issue"
            )

    with error_boundary("user search and validation", LayerType.API):
        try:
            # Search for user
            search_filter = f"(sAMAccountName={username})"
            admin_conn.search(LDAP_BASE_DN, search_filter, attributes=['cn', 'mail', 'title', 'memberOf'])
            
            if not admin_conn.entries:
                raise AuthenticationException(
                    message="User not found",
                    details=f"No user found with username: {username}",
                    context={'username': username, 'search_base': LDAP_BASE_DN},
                    suggested_action="Verify username spelling and account existence"
                )

            user_entry = admin_conn.entries[0]
            user_dn = user_entry.entry_dn

            # Test user credentials
            user_conn = Connection(
                Server(LDAP_URL),
                user=user_dn,
                password=password,
                auto_bind=True
            )
            user_conn.unbind()

        except LDAPInvalidCredentialsResult:
            raise AuthenticationException(
                message="Invalid credentials",
                details="Username or password is incorrect",
                context={'username': username},
                suggested_action="Check your username and password and try again"
            )
        except LDAPException as e:
            raise AuthenticationException(
                message="Authentication failed",
                details=f"LDAP authentication error: {str(e)}",
                original_exception=e,
                context={'username': username, 'user_dn': user_dn if 'user_dn' in locals() else None},
                suggested_action="Contact system administrator if problem persists"
            )

    with error_boundary("user information extraction", LayerType.API):
        try:
            # Extract user information
            user_info = {"username": username, "workspace_id": username}

            # Extract nickname/display name from cn attribute
            if hasattr(user_entry, 'cn'):
                cn_value = getattr(user_entry, 'cn').value
                if cn_value:
                    user_info["nickname"] = str(cn_value)
                    user_info["display_name"] = str(cn_value)

            if hasattr(user_entry, 'mail'):
                mail_value = getattr(user_entry, 'mail').value
                if mail_value:
                    user_info["email"] = str(mail_value)

            if hasattr(user_entry, 'title'):
                title_value = getattr(user_entry, 'title').value
                if title_value:
                    user_info["title"] = str(title_value)

            # Extract department information from memberOf groups
            department = None
            if hasattr(user_entry, 'memberOf'):
                memberof_value = getattr(user_entry, 'memberOf').value
                if memberof_value:
                    groups = memberof_value if isinstance(memberof_value, list) else [memberof_value]
                    user_info["groups"] = [str(group) for group in groups]

                    # Extract department from group names (look for dept_ prefix or OU=)
                    for group in groups:
                        group_str = str(group).lower()
                        if 'ou=' in group_str:
                            # Extract OU value as department
                            ou_parts = group_str.split('ou=')
                            if len(ou_parts) > 1:
                                dept_part = ou_parts[1].split(',')[0]
                                department = dept_part.strip()
                                break
                        elif 'dept_' in group_str or 'department_' in group_str:
                            # Extract department from group name
                            if 'dept_' in group_str:
                                department = group_str.split('dept_')[1].split(',')[0].strip()
                            elif 'department_' in group_str:
                                department = group_str.split('department_')[1].split(',')[0].strip()
                            break

            # Set department in user_info and workspace_id
            if department:
                user_info["department"] = department
                user_info["workspace_id"] = department
            else:
                user_info["department"] = username  # Fallback to username
                user_info["workspace_id"] = username

            admin_conn.unbind()
            logger.info(f"LDAP authentication successful for user: {username}")
            return user_info

        except Exception as e:
            raise DataProcessingException(
                message="Failed to extract user information",
                details=f"Error processing LDAP user data: {str(e)}",
                original_exception=e,
                context={'username': username, 'user_dn': user_dn if 'user_dn' in locals() else None},
                suggested_action="Contact system administrator - user data processing issue"
            )

@log_and_reraise(logger, "access token creation")
def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """Create JWT access token with unified error handling."""
    set_operation_context("create_access_token")
    
    with error_boundary("token data preparation", LayerType.API):
        try:
            to_encode = data.copy()
            if expires_delta:
                expire = datetime.utcnow() + expires_delta
            else:
                expire = datetime.utcnow() + timedelta(minutes=15)
            to_encode.update({"exp": expire})
            
            encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
            return encoded_jwt
            
        except Exception as e:
            raise AuthenticationException(
                message="Failed to create access token",
                details=f"JWT encoding failed: {str(e)}",
                original_exception=e,
                context={'data_keys': list(data.keys()), 'algorithm': ALGORITHM},
                suggested_action="Contact system administrator - token generation issue"
            )

@handle_layer_boundary(LayerType.API, "token validation")
def get_current_user(token: str = Depends(oauth2_scheme)):
    with error_boundary("JWT token validation", LayerType.API):
        # Validate token format first
        if not token or not isinstance(token, str):
            raise AuthenticationException(
                message="Invalid token format",
                details="Token is empty or not a string",
                context={'token_type': type(token).__name__, 'token_length': len(token) if token else 0},
                suggested_action="Please log in again to get a valid token"
            )

        # Check for basic JWT structure (should have 3 parts separated by dots)
        token_parts = token.split('.')
        if len(token_parts) != 3:
            raise AuthenticationException(
                message="Malformed JWT token",
                details=f"JWT token should have 3 parts separated by dots, but found {len(token_parts)} parts",
                context={'token_parts_count': len(token_parts), 'token_preview': token[:20] + '...' if len(token) > 20 else token},
                suggested_action="Please log in again to get a properly formatted token"
            )

        try:
            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            username: str = payload.get("sub")
            if username is None:
                raise AuthenticationException(
                    message="Invalid token: missing username",
                    details="Token payload does not contain 'sub' field",
                    context={'token_payload_keys': list(payload.keys()) if payload else []},
                    suggested_action="Please log in again to get a valid token with user information"
                )
            return username
        except JWTError as e:
            # Provide more specific error messages based on the type of JWT error
            error_type = type(e).__name__
            if "ExpiredSignature" in error_type:
                raise AuthenticationException(
                    message="Token has expired",
                    details=f"JWT token expired: {str(e)}",
                    original_exception=e,
                    context={'error_type': error_type},
                    suggested_action="Please log in again to get a new token"
                )
            elif "InvalidSignature" in error_type:
                raise AuthenticationException(
                    message="Invalid token signature",
                    details=f"JWT signature validation failed: {str(e)}",
                    original_exception=e,
                    context={'error_type': error_type},
                    suggested_action="Please log in again - token may have been tampered with"
                )
            else:
                raise AuthenticationException(
                    message="Invalid or malformed token",
                    details=f"JWT validation failed ({error_type}): {str(e)}",
                    original_exception=e,
                    context={'error_type': error_type},
                    suggested_action="Please log in again to get a new token"
                )

@handle_layer_boundary(LayerType.API, "full user info extraction")
def get_current_user_full(token: str = Depends(oauth2_scheme)):
    """Extract full user information from JWT token"""
    with error_boundary("JWT token validation for full user info", LayerType.API):
        # Validate token format first
        if not token or not isinstance(token, str):
            raise AuthenticationException(
                message="Invalid token format",
                details="Token is empty or not a string",
                context={'token_type': type(token).__name__, 'token_length': len(token) if token else 0},
                suggested_action="Please log in again to get a valid token"
            )

        try:
            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            username: str = payload.get("sub")
            if username is None:
                raise AuthenticationException(
                    message="Invalid token: missing username",
                    details="Token payload does not contain 'sub' field",
                    context={'token_payload_keys': list(payload.keys()) if payload else []},
                    suggested_action="Please log in again to get a valid token with user information"
                )

            # Extract full user information from token
            user_info = {
                "username": username,
                "workspace_id": payload.get("workspace", username),
                "display_name": payload.get("display_name", username),
                "nickname": payload.get("nickname", username),
                "department": payload.get("department", payload.get("workspace", username))
            }

            return user_info

        except JWTError as e:
            # Provide more specific error messages based on the type of JWT error
            error_type = type(e).__name__
            raise AuthenticationException(
                message="Token validation failed",
                details=f"JWT validation error: {str(e)}",
                original_exception=e,
                context={'error_type': error_type},
                suggested_action="Please log in again to get a new token"
            )

@handle_layer_boundary(LayerType.API, "workspace validation")
def get_current_workspace(token: str = Depends(oauth2_scheme)):
    with error_boundary("JWT workspace token validation", LayerType.API):
        # Validate token format first
        if not token or not isinstance(token, str):
            raise AuthenticationException(
                message="Invalid token format",
                details="Token is empty or not a string",
                context={'token_type': type(token).__name__, 'token_length': len(token) if token else 0},
                suggested_action="Please log in again to get a valid token"
            )

        # Check for basic JWT structure (should have 3 parts separated by dots)
        token_parts = token.split('.')
        if len(token_parts) != 3:
            raise AuthenticationException(
                message="Malformed JWT token",
                details=f"JWT token should have 3 parts separated by dots, but found {len(token_parts)} parts",
                context={'token_parts_count': len(token_parts), 'token_preview': token[:20] + '...' if len(token) > 20 else token},
                suggested_action="Please log in again to get a properly formatted token"
            )

        try:
            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            workspace_id: str = payload.get("workspace")
            if workspace_id is None:
                raise AuthenticationException(
                    message="Invalid token: missing workspace",
                    details="Token payload does not contain 'workspace' field",
                    context={'token_payload_keys': list(payload.keys()) if payload else []},
                    suggested_action="Please log in again to get a valid token with workspace information"
                )
            return workspace_id
        except JWTError as e:
            # Provide more specific error messages based on the type of JWT error
            error_type = type(e).__name__
            if "ExpiredSignature" in error_type:
                raise AuthenticationException(
                    message="Token has expired",
                    details=f"JWT token expired: {str(e)}",
                    original_exception=e,
                    context={'error_type': error_type},
                    suggested_action="Please log in again to get a new token"
                )
            elif "InvalidSignature" in error_type:
                raise AuthenticationException(
                    message="Invalid token signature",
                    details=f"JWT signature validation failed: {str(e)}",
                    original_exception=e,
                    context={'error_type': error_type},
                    suggested_action="Please log in again - token may have been tampered with"
                )
            else:
                raise AuthenticationException(
                    message="Invalid or malformed token",
                    details=f"JWT validation failed ({error_type}): {str(e)}",
                    original_exception=e,
                    context={'error_type': error_type},
                    suggested_action="Please log in again to get a new token"
                )

# ================== Authentication Endpoints ==================
class TokenResponse(BaseModel):
    access_token: str
    token_type: str

@app.post("/token", response_model=TokenResponse)
@handle_layer_boundary(LayerType.API, "user login")
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends()):
    set_operation_context("user_login")
    
    with error_boundary("user authentication", LayerType.API):
        if AUTH_MODE == "db":
            user = authenticate_user_db(form_data.username, form_data.password)
        else:
            user = authenticate_user_ldap(form_data.username, form_data.password)
        
        if not user:
            raise AuthenticationException(
                message="Authentication failed",
                details="Invalid username or password",
                context={'username': form_data.username, 'auth_mode': AUTH_MODE},
                suggested_action="Check your credentials and try again"
            )
    
    with error_boundary("token generation", LayerType.API):
        access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)

        # Prepare token data with comprehensive user information
        token_data = {
            "sub": user.username if hasattr(user, 'username') else user['username'],
            "workspace": user.workspace_id if hasattr(user, 'workspace_id') else user['workspace_id']
        }

        # Add additional user information to token for LDAP users
        if isinstance(user, dict):  # LDAP user
            if 'display_name' in user:
                token_data["display_name"] = user['display_name']
            if 'nickname' in user:
                token_data["nickname"] = user['nickname']
            if 'department' in user:
                token_data["department"] = user['department']
        else:  # DB user
            # For DB users, use username as display name and nickname
            token_data["display_name"] = user.username
            token_data["nickname"] = user.username
            token_data["department"] = user.workspace_id

        access_token = create_access_token(
            data=token_data,
            expires_delta=access_token_expires
        )
        return {"access_token": access_token, "token_type": "bearer"}

@app.get("/user/me")
@handle_layer_boundary(LayerType.API, "user info retrieval")
async def get_current_user_info(
    user_info: dict = Depends(get_current_user_full)
):
    set_operation_context("get_current_user_info")

    with error_boundary("user information assembly", LayerType.API):
        # Return the full user information extracted from the JWT token
        return user_info

# ================== Template Management Endpoints ==================
class TemplateCreateRequest(BaseModel):
    name: str
    content: str

class TemplateResponse(BaseModel):
    id: int
    name: str
    path: str

@app.post("/templates", response_model=TemplateResponse)
@handle_layer_boundary(LayerType.API, "template creation")
async def create_template(
    template: TemplateCreateRequest,
    workspace_id: str = Depends(get_current_workspace)
):
    set_operation_context("create_template")
    
    if AUTH_MODE != "db":
        raise ConfigurationException(
            message="Template management not available",
            details=f"Template creation only available in DB mode, current mode: {AUTH_MODE}",
            context={'auth_mode': AUTH_MODE, 'template_name': template.name},
            suggested_action="Switch to database authentication mode to enable template management"
        )
    
    with error_boundary("template directory setup", LayerType.API):
        try:
            template_dir = os.path.join("templates", workspace_id)
            os.makedirs(template_dir, exist_ok=True)
        except OSError as e:
            raise FileProcessingException(
                message="Failed to create template directory",
                file_path=template_dir,
                details=f"Directory creation failed: {str(e)}",
                original_exception=e,
                context={'workspace_id': workspace_id, 'template_dir': template_dir},
                suggested_action="Check directory permissions and disk space"
            )
    
    with error_boundary("template file creation", LayerType.API):
        try:
            template_path = os.path.join(template_dir, f"{template.name}.md")
            with open(template_path, 'w', encoding='utf-8') as f:
                f.write(template.content)
        except OSError as e:
            raise FileProcessingException(
                message="Failed to save template file",
                file_path=template_path,
                details=f"File write operation failed: {str(e)}",
                original_exception=e,
                context={'template_name': template.name, 'template_path': template_path},
                suggested_action="Check file permissions and disk space"
            )
    
    with error_boundary("database template record", LayerType.API):
        db = SessionLocal()
        try:
            db_template = Template(
                name=template.name,
                path=template_path,
                workspace_id=workspace_id
            )
            db.add(db_template)
            db.commit()
            db.refresh(db_template)
            return db_template
        except Exception as e:
            db.rollback()
            raise DataProcessingException(
                message="Failed to save template to database",
                details=f"Database operation failed: {str(e)}",
                original_exception=e,
                context={'template_name': template.name, 'workspace_id': workspace_id},
                suggested_action="Check database connection and try again"
            )
        finally:
            db.close()

@app.get("/templates", response_model=List[TemplateResponse])
@handle_layer_boundary(LayerType.API, "template retrieval")
async def get_templates(workspace_id: str = Depends(get_current_workspace)):
    set_operation_context("get_templates")
    
    if AUTH_MODE == "db":
        with error_boundary("database template query", LayerType.API):
            db = SessionLocal()
            try:
                templates = db.query(Template).filter(Template.workspace_id == workspace_id).all()
                return templates
            except Exception as e:
                raise DataProcessingException(
                    message="Failed to retrieve templates from database",
                    details=f"Database query failed for workspace {workspace_id}: {str(e)}",
                    original_exception=e,
                    context={'workspace_id': workspace_id, 'auth_mode': AUTH_MODE},
                    suggested_action="Check database connection and workspace permissions"
                )
            finally:
                db.close()
    else:
        # For LDAP mode, scan filesystem
        with error_boundary("filesystem template scan", LayerType.API):
            template_dir = os.path.join("templates", workspace_id)
            templates = []
            
            if not os.path.exists(template_dir):
                logger.info(f"Template directory does not exist: {template_dir}")
                return templates
            
            try:
                for i, filename in enumerate(os.listdir(template_dir)):
                    if filename.endswith('.md'):
                        templates.append({
                            "id": i,
                            "name": filename[:-3],  # Remove .md extension
                            "path": os.path.join(template_dir, filename)
                        })
                return templates
            except OSError as e:
                raise FileProcessingException(
                    message="Failed to scan template directory",
                    file_path=template_dir,
                    details=f"Directory scan failed: {str(e)}",
                    original_exception=e,
                    context={'workspace_id': workspace_id, 'template_dir': template_dir},
                    suggested_action="Check directory permissions and file system access"
                )

@app.delete("/templates/{template_id}")
@handle_layer_boundary(LayerType.API, "template deletion")
async def delete_template(template_id: int, workspace_id: str = Depends(get_current_workspace)):
    set_operation_context("delete_template")
    
    if AUTH_MODE != "db":
        raise ConfigurationException(
            message="Template deletion not available in current mode",
            details=f"Template deletion only available in DB mode, current mode: {AUTH_MODE}",
            context={'auth_mode': AUTH_MODE, 'template_id': template_id},
            suggested_action="Switch to database authentication mode to enable template deletion"
        )
    
    with error_boundary("template deletion process", LayerType.API):
        db = SessionLocal()
        try:
            template = db.query(Template).filter(
                Template.id == template_id,
                Template.workspace_id == workspace_id
            ).first()
            
            if not template:
                raise ValidationException(
                    message="Template not found",
                    field_name="template_id",
                    details=f"Template with ID {template_id} not found in workspace {workspace_id}",
                    context={'template_id': template_id, 'workspace_id': workspace_id},
                    suggested_action="Verify the template ID and ensure it belongs to your workspace"
                )
            
            # Delete file
            if os.path.exists(template.path):
                try:
                    os.remove(template.path)
                except OSError as e:
                    raise FileProcessingException(
                        message="Failed to delete template file",
                        file_path=template.path,
                        details=f"File deletion failed: {str(e)}",
                        original_exception=e,
                        context={'template_id': template_id, 'file_path': template.path},
                        suggested_action="Check file permissions and disk space"
                    )
            
            # Delete from database
            db.delete(template)
            db.commit()
            return {"status": "success"}
            
        except Exception as e:
            db.rollback()
            if isinstance(e, BaseAppException):
                raise
            raise DataProcessingException(
                message="Database operation failed during template deletion",
                details=f"Unexpected database error: {str(e)}",
                original_exception=e,
                context={'template_id': template_id, 'workspace_id': workspace_id},
                suggested_action="Check database connection and try again"
            )
        finally:
            db.close()

# ================== Report Agent Endpoints ==================
class ReportAgentCreateRequest(BaseModel):
    config: dict

class ReportAgentProcessRequest(BaseModel):
    session_id: str
    user_input: str
    current_company: Optional[str] = ''  # Make optional with empty string default
    current_year: Optional[str] = ''     # Make optional with empty string default
    current_quarter: Optional[str] = ''  # Make optional with empty string default
    extra_context: Dict[str, Any] = {}

@app.post("/reportagent/create")
@handle_layer_boundary(LayerType.API, "report agent creation")
def create_report_agent(
    request: ReportAgentCreateRequest,
    workspace_id: str = Depends(get_current_workspace)
):
    set_operation_context("create_report_agent")
    logger.info("Creating new report agent session")
    logger.debug(f"Request config: {request.config}")
    
    with error_boundary("agent session creation", LayerType.API):
        sid = str(uuid.uuid4())
        
        # Validate and prepare configuration
        config_dict = request.config.copy()
        config_dict.update({
            "api_key": os.getenv("OPENAI_API_KEY", ""),
            "api_base": os.getenv("OPENAI_API_BASE", ""),
            "llm_backend": os.getenv("LLM_BACKEND", "OLLAMA"),
            "model_name": os.getenv("MODEL_ID", "QwQ-32b"),
            "log_level": "DEBUG",
            "log_file": "server.log",
            "workspace_id": workspace_id
        })
        
        # Validate required fields
        if not config_dict.get("workspace_id"):
            raise ValidationException(
                message="Workspace ID is required",
                field_name="workspace_id",
                details="workspace_id missing from configuration",
                context={'provided_config': list(config_dict.keys())},
                suggested_action="Ensure workspace_id is provided in the request"
            )
        
        try:
            config = AgentConfig(**config_dict)
            agent = ReportAgent(config)
            sessions["report_agent"][sid] = agent
            
            logger.info(f"Created new agent session: {sid}")
            logger.debug(f"Initial agent state: company={agent.company_name}, year={agent.report_year}, template={agent.template}")
            
            return {
                "session_id": sid,
                "message": "Session created",
                "status": "success",
                "agent_state": {
                    "company": agent.company_name,
                    "year": agent.report_year,
                    "quarter": getattr(agent, "report_quarter", None),
                    "template": agent.template,
                    "required_files": agent.required_files
                }
            }
        except Exception as e:
            raise AgentException(
                message="Failed to create report agent",
                agent_type="ReportAgent",
                details=f"Agent initialization failed: {str(e)}",
                original_exception=e,
                context={'session_id': sid, 'config_keys': list(config_dict.keys())},
                suggested_action="Check configuration parameters and environment variables"
            )

@app.post("/reportagent/process")
@handle_layer_boundary(LayerType.API, "report agent message processing")
def process_message(
    request: ReportAgentProcessRequest,
    current_workspace: str = Depends(get_current_workspace)
):
    """Process message through report agent"""
    set_operation_context("process_agent_message")
    
    with error_boundary("agent message processing", LayerType.API):
        try:
            agent = sessions["report_agent"][request.session_id]
            logger.debug(f"Processing message for agent: {agent.company_name} (year: {agent.report_year}, quarter: {agent.report_quarter})")

            result = agent.process_message(
                request.user_input,
                request.current_company,
                request.current_year,
                request.current_quarter,
                request.extra_context
            )
            logger.debug(f"Agent processing result: {result}")

            # Update agent_state with current agent values
            agent_state = {
                "company": agent.company_name,
                "year": agent.report_year,
                "quarter": getattr(agent, "report_quarter", None),
                "template": agent.template,
                "required_files": agent.required_files
            }

            # Flatten the response structure to match frontend expectations
            # Merge the agent result with metadata fields
            response = {
                **result,  # This includes: company, year, quarter, message, status
                "agent_state": agent_state,
                "session_id": request.session_id
            }

            return response
            
        except Exception as e:
            raise AgentException(
                message="Agent processing failed",
                agent_type="ReportAgent",
                details=f"Message processing error: {str(e)}",
                original_exception=e,
                context={
                    'session_id': request.session_id,
                    'user_input_length': len(request.user_input),
                    'current_company': request.current_company,
                    'current_year': request.current_year
                },
                suggested_action="Check input format and try again, or create a new session"
            )

@app.get("/reportagent/sessions/{session_id}/status")
@handle_layer_boundary(LayerType.API, "session status check")
def get_session_status(
    session_id: str,
    workspace_id: str = Depends(get_current_workspace)
):
    set_operation_context("get_session_status")
    logger.debug(f"Checking status for session {session_id}")
    
    with error_boundary("session status retrieval", LayerType.API):
        if session_id not in sessions.get("report_agent", {}):
            return {
                "exists": False,
                "message": "Session not found",
                "session_id": session_id
            }
        
        try:
            agent = sessions["report_agent"][session_id]
            return {
                "exists": True,
                "company": agent.company_name,
                "year": agent.report_year,
                "quarter": getattr(agent, "report_quarter", None),
                "workspace_id": agent.workspace_id,
                "session_id": session_id
            }
        except Exception as e:
            raise AgentException(
                message="Failed to retrieve session status",
                agent_type="ReportAgent",
                details=f"Session access error: {str(e)}",
                original_exception=e,
                context={'session_id': session_id, 'workspace_id': workspace_id},
                suggested_action="Verify session ID and try creating a new session if needed"
            )

# ================== RAG Workflow Endpoints ==================
class RAGWorkflowExecuteRequest(BaseModel):
    workspace_path: str
    template_file: str

@app.post("/ragworkflow/execute")
@handle_layer_boundary(LayerType.API, "RAG workflow execution")
def execute_rag_workflow(
    request: RAGWorkflowExecuteRequest,
    workspace_id: str = Depends(get_current_workspace)
):
    logger.info(f"Executing RAG workflow for template: {request.template_file}")
    logger.debug(f"Workspace path: {request.workspace_path}")
    logger.debug(f"Current workspace_id: {workspace_id}")

    # Normalize path separators for cross-platform compatibility
    normalized_workspace_path = request.workspace_path.replace('\\', '/')
    expected_prefix = f"workspaces/{workspace_id}"

    logger.debug(f"Normalized workspace path: {normalized_workspace_path}")
    logger.debug(f"Expected prefix: {expected_prefix}")

    # Verify workspace matches - this is a validation error
    if not normalized_workspace_path.startswith(expected_prefix):
        raise ValidationException(
            message="Workspace access denied",
            details=f"Path '{normalized_workspace_path}' not allowed for workspace '{workspace_id}'",
            context={
                'requested_path': normalized_workspace_path,
                'expected_prefix': expected_prefix,
                'workspace_id': workspace_id
            },
            suggested_action="Please ensure you are accessing files within your assigned workspace"
        )

    # Execute workflow - this crosses into business logic layer
    with error_boundary("RAG workflow execution", LayerType.API):
        workflow = RAGWorkflow(**request.model_dump())
        result = workflow.execute()
        logger.info(f"RAG workflow completed with status: {result.get('status', 'unknown')}")
        logger.debug(f"Workflow result: {result}")
        return result

# ================== Markdown Tools Endpoints ==================
@app.post("/mdtools/convert")
@handle_layer_boundary(LayerType.API, "markdown conversion")
def convert_md_tools(
    workspace_path: str, 
    template_file: str,
    workspace_id: str = Depends(get_current_workspace)
):
    set_operation_context("convert_md_tools")
    logger.info(f"Converting markdown to docx for template: {template_file}")
    logger.debug(f"Workspace path: {workspace_path}")
    logger.debug(f"Current workspace_id: {workspace_id}")
    
    with error_boundary("workspace path validation", LayerType.API):
        # Normalize path separators for cross-platform compatibility
        normalized_workspace_path = workspace_path.replace('\\', '/')
        expected_prefix = f"workspaces/{workspace_id}"
        
        logger.debug(f"Normalized workspace path: {normalized_workspace_path}")
        logger.debug(f"Expected prefix: {expected_prefix}")
        
        # Verify workspace matches
        if not normalized_workspace_path.startswith(expected_prefix):
            raise ValidationException(
                message="Workspace access denied",
                field_name="workspace_path",
                details=f"Path '{normalized_workspace_path}' not allowed for workspace '{workspace_id}'",
                context={
                    'requested_path': normalized_workspace_path,
                    'expected_prefix': expected_prefix,
                    'workspace_id': workspace_id
                },
                suggested_action="Ensure you are accessing files within your assigned workspace"
            )
    
    with error_boundary("markdown conversion process", LayerType.API):
        try:
            from app.utils.md_tools import MarkdownTools
            md_tools = MarkdownTools()
            
            logger.debug(f"Converting markdown to PDF: {workspace_path}")
            pdf_path = md_tools.convert_to_pdf(workspace_path)
            
            logger.debug(f"Converting PDF to DOCX: {pdf_path}")
            docx_path = md_tools.convert_pdf_to_docx(pdf_path)
            
            logger.info(f"Conversion successful - PDF: {pdf_path}, DOCX: {docx_path}")
            return {"pdf_path": pdf_path, "docx_path": docx_path}
            
        except Exception as e:
            raise FileProcessingException(
                message="Markdown conversion failed",
                file_path=workspace_path,
                details=f"Conversion process failed: {str(e)}",
                original_exception=e,
                context={
                    'workspace_path': workspace_path,
                    'template_file': template_file,
                    'workspace_id': workspace_id
                },
                suggested_action="Check file format and ensure all required dependencies are installed"
            )

class ReportScanRequest(BaseModel):
    workspace_id: str

@app.post("/reportagent/scan")
@handle_layer_boundary(LayerType.API, "report scanning")
def scan_reports(
    request: ReportScanRequest,
    current_workspace: str = Depends(get_current_workspace)
):
    set_operation_context("scan_reports")
    logger.info(f"Scanning reports for workspace: {request.workspace_id}")
    
    with error_boundary("workspace access validation", LayerType.API):
        # Verify workspace matches
        if request.workspace_id != current_workspace:
            raise ValidationException(
                message="Workspace access denied",
                field_name="workspace_id",
                details=f"Requested workspace '{request.workspace_id}' does not match current workspace '{current_workspace}'",
                context={'requested_workspace': request.workspace_id, 'current_workspace': current_workspace},
                suggested_action="Ensure you are accessing your assigned workspace"
            )
    
    with error_boundary("report file scanning", LayerType.API):
        available_reports = []
        workspace_root = os.path.join("workspaces", request.workspace_id)
        
        if not os.path.exists(workspace_root):
            logger.info(f"Workspace root does not exist: {workspace_root}")
            return {
                "status": "success",
                "available_reports": available_reports,
                "workspace_scanned": workspace_root
            }
        
        try:
            for company in os.listdir(workspace_root):
                company_path = os.path.join(workspace_root, company)
                if os.path.isdir(company_path):
                    for year in os.listdir(company_path):
                        year_path = os.path.join(company_path, year)
                        if os.path.isdir(year_path):
                            report_path = os.path.join(year_path, f"{company}_report.docx")
                            if os.path.exists(report_path):
                                available_reports.append({
                                    'company': company,
                                    'year': year,
                                    'path': report_path
                                })
            
            logger.info(f"Found {len(available_reports)} reports")
            return {
                "status": "success",
                "available_reports": available_reports,
                "workspace_scanned": workspace_root
            }
            
        except OSError as e:
            raise FileProcessingException(
                message="Failed to scan workspace for reports",
                file_path=workspace_root,
                details=f"Directory scanning failed: {str(e)}",
                original_exception=e,
                context={'workspace_id': request.workspace_id, 'workspace_root': workspace_root},
                suggested_action="Check directory permissions and file system access"
            )

# ================== Health Check ==================
@app.get("/health")
@handle_layer_boundary(LayerType.API, "health check")
def health_check():
    set_operation_context("health_check")
    logger.debug("Performing health check")
    
    with error_boundary("health status collection", LayerType.API):
        try:
            status = {
                "status": "healthy", 
                "services": list(sessions.keys()),
                "timestamp": datetime.utcnow().isoformat()
            }
            logger.debug(f"Health check result: {status}")
            return status
        except Exception as e:
            raise DataProcessingException(
                message="Health check failed",
                details=f"Failed to collect system status: {str(e)}",
                original_exception=e,
                context={'available_sessions': list(sessions.keys()) if sessions else []},
                suggested_action="Check system resources and service availability"
            )

if __name__ == "__main__":
    import uvicorn
    from dotenv import load_dotenv
    import os
    
    logger.info("Starting server...")
    
    load_dotenv(override=True)
    logger.debug(f"Environment variables loaded: ")
    logger.debug(f"LLM_BACKEND={os.getenv('LLM_BACKEND')}")
    logger.debug(f"OPENAI_API_BASE={os.getenv('OPENAI_API_BASE')}")
    logger.debug(f"OLLAMA_API_BASE={os.getenv('OLLAMA_API_BASE')}")
    logger.debug(f"MODEL_ID={os.getenv('MODEL_ID')}")
    logger.debug(f"EMBEDDING_MODEL={os.getenv('EMBEDDING_MODEL')}")
    host = os.getenv("SERVER_HOST", "0.0.0.0")
    port = int(os.getenv("REPORT_SERVER_PORT", "8100"))
    
    logger.info(f"Starting server on {host}:{port}")
    uvicorn.run(app, host=host, port=port, log_config=None)

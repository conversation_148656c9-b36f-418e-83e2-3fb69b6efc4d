#!/usr/bin/env python3
"""
Crawler Status and Screenshot Display Test Script

This script tests the crawler status response processing and screenshot display fixes to ensure:
1. Screenshot data is not truncated during status processing
2. QR code screenshots are properly displayed when crawler is not logged in
3. Fallback handling when no screenshot is available
4. Status updates preserve screenshot data
5. Proper error handling and user feedback

Usage:
    python test_crawler_status_fixes.py
"""

import sys
import re
import logging
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CrawlerStatusTest:
    """Test suite for crawler status and screenshot handling fixes"""
    
    def __init__(self):
        self.test_results = []
        
    def log_test_result(self, test_name: str, passed: bool, message: str = ""):
        """Log test result"""
        status = "PASS" if passed else "FAIL"
        result = f"[{status}] {test_name}: {message}"
        self.test_results.append((test_name, passed, message))
        logger.info(result)
    
    def test_screenshot_data_not_truncated(self):
        """Test that screenshot data is not truncated in check_crawler_status()"""
        logger.info("Testing Screenshot Data Not Truncated...")
        
        try:
            main_app_path = Path("app/main.py")
            if main_app_path.exists():
                content = main_app_path.read_text(encoding='utf-8')
                
                # Check for the specific problematic truncation pattern
                truncation_patterns = [
                    r"result\.get\('screen', None\)\[:20\]",
                    r"'screen'.*\[:20\]"
                ]

                found_truncation = False
                for pattern in truncation_patterns:
                    if re.search(pattern, content):
                        found_truncation = True
                        break
                
                # Check for proper full screenshot handling
                full_screenshot_patterns = [
                    "result.get('screen', None)  # Keep full screenshot data",
                    "'screen': result.get('screen', None)"
                ]
                
                found_full_handling = any(pattern in content for pattern in full_screenshot_patterns)
                
                if not found_truncation and found_full_handling:
                    self.log_test_result("Screenshot Data Not Truncated", True, 
                                       "Screenshot data is properly preserved")
                else:
                    self.log_test_result("Screenshot Data Not Truncated", False, 
                                       f"Truncation found: {found_truncation}, Full handling: {found_full_handling}")
            else:
                self.log_test_result("Screenshot Data Not Truncated", False, "Main app file not found")
                
        except Exception as e:
            self.log_test_result("Screenshot Data Not Truncated", False, f"Error: {str(e)}")
    
    def test_status_updates_preserve_screenshot(self):
        """Test that status updates preserve screenshot data"""
        logger.info("Testing Status Updates Preserve Screenshot...")
        
        try:
            main_app_path = Path("app/main.py")
            if main_app_path.exists():
                content = main_app_path.read_text(encoding='utf-8')
                
                # Check for screenshot preservation in status updates
                preservation_patterns = [
                    "'screen': status_result.get('screen', None)  # Preserve screenshot data",
                    "status_result.get('screen', None)"
                ]
                
                found_patterns = []
                for pattern in preservation_patterns:
                    if pattern in content:
                        found_patterns.append(pattern)
                
                if len(found_patterns) >= 1:
                    self.log_test_result("Status Updates Preserve Screenshot", True, 
                                       f"Found {len(found_patterns)} screenshot preservation patterns")
                else:
                    self.log_test_result("Status Updates Preserve Screenshot", False, 
                                       "No screenshot preservation patterns found")
            else:
                self.log_test_result("Status Updates Preserve Screenshot", False, "Main app file not found")
                
        except Exception as e:
            self.log_test_result("Status Updates Preserve Screenshot", False, f"Error: {str(e)}")
    
    def test_fallback_screenshot_handling(self):
        """Test that fallback handling is implemented when no screenshot is available"""
        logger.info("Testing Fallback Screenshot Handling...")
        
        try:
            main_app_path = Path("app/main.py")
            if main_app_path.exists():
                content = main_app_path.read_text(encoding='utf-8')
                
                # Check for fallback handling patterns
                fallback_patterns = [
                    "else:",  # Should have else clause for no screenshot
                    "正在获取登录二维码",
                    "无法获取登录二维码",
                    "refresh_crawler_screenshot()",
                    "st.rerun()  # Refresh to show the screenshot"
                ]
                
                found_patterns = []
                for pattern in fallback_patterns:
                    if pattern in content:
                        found_patterns.append(pattern)
                
                if len(found_patterns) >= 4:
                    self.log_test_result("Fallback Screenshot Handling", True, 
                                       f"Found {len(found_patterns)}/5 fallback patterns")
                else:
                    self.log_test_result("Fallback Screenshot Handling", False, 
                                       f"Only found {len(found_patterns)}/5 fallback patterns")
            else:
                self.log_test_result("Fallback Screenshot Handling", False, "Main app file not found")
                
        except Exception as e:
            self.log_test_result("Fallback Screenshot Handling", False, f"Error: {str(e)}")
    
    def test_improved_logging_without_spam(self):
        """Test that logging is improved without screenshot data spam"""
        logger.info("Testing Improved Logging Without Spam...")
        
        try:
            main_app_path = Path("app/main.py")
            if main_app_path.exists():
                content = main_app_path.read_text(encoding='utf-8')
                
                # Check for improved logging patterns
                logging_patterns = [
                    "log_info = {k: v for k, v in status_info.items() if k != 'screen'}",
                    "<base64 data:",
                    "chars>",
                    "logger.debug(f\"爬虫状态: {log_info}\")"
                ]
                
                found_patterns = []
                for pattern in logging_patterns:
                    if pattern in content:
                        found_patterns.append(pattern)
                
                if len(found_patterns) >= 3:
                    self.log_test_result("Improved Logging Without Spam", True, 
                                       f"Found {len(found_patterns)}/4 improved logging patterns")
                else:
                    self.log_test_result("Improved Logging Without Spam", False, 
                                       f"Only found {len(found_patterns)}/4 improved logging patterns")
            else:
                self.log_test_result("Improved Logging Without Spam", False, "Main app file not found")
                
        except Exception as e:
            self.log_test_result("Improved Logging Without Spam", False, f"Error: {str(e)}")
    
    def test_initial_screenshot_acquisition(self):
        """Test that initial screenshot acquisition is implemented"""
        logger.info("Testing Initial Screenshot Acquisition...")
        
        try:
            main_app_path = Path("app/main.py")
            if main_app_path.exists():
                content = main_app_path.read_text(encoding='utf-8')
                
                # Check for initial screenshot acquisition patterns
                acquisition_patterns = [
                    "如果登录后仍未成功，且没有截图，尝试获取截图",
                    "Getting screenshot for QR code display",
                    "screenshot = await refresh_crawler_screenshot()",
                    "st.session_state.crawler_status['screen'] = screenshot",
                    "has_screen: {bool(crawler_status.get('screen'))}"
                ]
                
                found_patterns = []
                for pattern in acquisition_patterns:
                    if pattern in content:
                        found_patterns.append(pattern)
                
                if len(found_patterns) >= 4:
                    self.log_test_result("Initial Screenshot Acquisition", True, 
                                       f"Found {len(found_patterns)}/5 acquisition patterns")
                else:
                    self.log_test_result("Initial Screenshot Acquisition", False, 
                                       f"Only found {len(found_patterns)}/5 acquisition patterns")
            else:
                self.log_test_result("Initial Screenshot Acquisition", False, "Main app file not found")
                
        except Exception as e:
            self.log_test_result("Initial Screenshot Acquisition", False, f"Error: {str(e)}")
    
    def test_qr_code_display_logic(self):
        """Test that QR code display logic is properly implemented"""
        logger.info("Testing QR Code Display Logic...")
        
        try:
            main_app_path = Path("app/main.py")
            if main_app_path.exists():
                content = main_app_path.read_text(encoding='utf-8')
                
                # Check for QR code display logic
                display_patterns = [
                    "二维码显示区域",
                    "if crawler_status.get('screen'):",
                    "await display_cropped_screenshot()",
                    "刷新二维码",
                    "重新登录"
                ]
                
                found_patterns = []
                for pattern in display_patterns:
                    if pattern in content:
                        found_patterns.append(pattern)
                
                if len(found_patterns) >= 4:
                    self.log_test_result("QR Code Display Logic", True, 
                                       f"Found {len(found_patterns)}/5 display patterns")
                else:
                    self.log_test_result("QR Code Display Logic", False, 
                                       f"Only found {len(found_patterns)}/5 display patterns")
            else:
                self.log_test_result("QR Code Display Logic", False, "Main app file not found")
                
        except Exception as e:
            self.log_test_result("QR Code Display Logic", False, f"Error: {str(e)}")
    
    def test_error_handling_and_user_feedback(self):
        """Test that proper error handling and user feedback is implemented"""
        logger.info("Testing Error Handling and User Feedback...")
        
        try:
            main_app_path = Path("app/main.py")
            if main_app_path.exists():
                content = main_app_path.read_text(encoding='utf-8')
                
                # Check for error handling and feedback patterns
                feedback_patterns = [
                    "st.info(\"📱 正在获取登录二维码...\")",
                    "st.warning(\"⚠️ 无法获取登录二维码",
                    "except Exception as e:",
                    "logger.warning(f\"Failed to get",
                    "try:"
                ]
                
                found_patterns = []
                for pattern in feedback_patterns:
                    if pattern in content:
                        found_patterns.append(pattern)
                
                if len(found_patterns) >= 4:
                    self.log_test_result("Error Handling and User Feedback", True, 
                                       f"Found {len(found_patterns)}/5 feedback patterns")
                else:
                    self.log_test_result("Error Handling and User Feedback", False, 
                                       f"Only found {len(found_patterns)}/5 feedback patterns")
            else:
                self.log_test_result("Error Handling and User Feedback", False, "Main app file not found")
                
        except Exception as e:
            self.log_test_result("Error Handling and User Feedback", False, f"Error: {str(e)}")
    
    def run_all_tests(self):
        """Run all crawler status and screenshot tests"""
        logger.info("Starting Crawler Status and Screenshot Test Suite...")
        logger.info("=" * 60)
        
        # Run all tests
        self.test_screenshot_data_not_truncated()
        self.test_status_updates_preserve_screenshot()
        self.test_fallback_screenshot_handling()
        self.test_improved_logging_without_spam()
        self.test_initial_screenshot_acquisition()
        self.test_qr_code_display_logic()
        self.test_error_handling_and_user_feedback()
        
        # Print summary
        logger.info("=" * 60)
        logger.info("Test Summary:")
        
        passed = sum(1 for _, result, _ in self.test_results if result)
        total = len(self.test_results)
        
        logger.info(f"Passed: {passed}/{total}")
        
        if passed == total:
            logger.info("🎉 All tests passed! Crawler status and screenshot handling is working correctly.")
        else:
            logger.warning(f"⚠️ {total - passed} tests failed. Please review the issues above.")
            
        return passed == total


def main():
    """Main test function"""
    test_suite = CrawlerStatusTest()
    success = test_suite.run_all_tests()
    
    if success:
        print("\n✅ Crawler status and screenshot test completed successfully!")
        return 0
    else:
        print("\n❌ Crawler status and screenshot test failed!")
        return 1


if __name__ == "__main__":
    try:
        result = main()
        sys.exit(result)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test failed with error: {str(e)}")
        sys.exit(1)

#!/usr/bin/env python3
"""
Streamlit Component Compatibility Test Script

This script tests the Streamlit component compatibility fixes to ensure:
1. No unsupported parameters are used with Streamlit components
2. All component calls are compatible with the current Streamlit version
3. Application starts without TypeError crashes
4. Functionality is preserved after parameter removal

Usage:
    python test_streamlit_compatibility.py
"""

import sys
import re
import logging
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class StreamlitCompatibilityTest:
    """Test suite for Streamlit component compatibility"""
    
    def __init__(self):
        self.test_results = []
        
    def log_test_result(self, test_name: str, passed: bool, message: str = ""):
        """Log test result"""
        status = "PASS" if passed else "FAIL"
        result = f"[{status}] {test_name}: {message}"
        self.test_results.append((test_name, passed, message))
        logger.info(result)
    
    def test_expander_key_parameter_removed(self):
        """Test that unsupported key parameter is removed from st.expander()"""
        logger.info("Testing Expander Key Parameter Removal...")
        
        try:
            main_app_path = Path("app/main.py")
            if main_app_path.exists():
                content = main_app_path.read_text(encoding='utf-8')
                
                # Check for problematic expander with key parameter
                problematic_patterns = [
                    'st.expander("爬虫登录", expanded=True, key="crawler_login_expander")',
                    'st.expander(.*key=.*)',
                ]
                
                found_issues = []
                for pattern in problematic_patterns:
                    if re.search(pattern, content):
                        found_issues.append(pattern)
                
                if len(found_issues) == 0:
                    self.log_test_result("Expander Key Parameter Removed", True, 
                                       "No unsupported key parameters found in expanders")
                else:
                    self.log_test_result("Expander Key Parameter Removed", False, 
                                       f"Found {len(found_issues)} problematic expander patterns")
            else:
                self.log_test_result("Expander Key Parameter Removed", False, "Main app file not found")
                
        except Exception as e:
            self.log_test_result("Expander Key Parameter Removed", False, f"Error: {str(e)}")
    
    def test_valid_expander_usage(self):
        """Test that expanders are used with valid parameters only"""
        logger.info("Testing Valid Expander Usage...")
        
        try:
            main_app_path = Path("app/main.py")
            if main_app_path.exists():
                content = main_app_path.read_text(encoding='utf-8')
                
                # Find all expander usages
                expander_pattern = r'st\.expander\([^)]+\)'
                expander_matches = re.findall(expander_pattern, content)
                
                valid_expanders = []
                invalid_expanders = []
                
                for match in expander_matches:
                    # Check if it contains unsupported parameters
                    if 'key=' in match:
                        invalid_expanders.append(match)
                    else:
                        valid_expanders.append(match)
                
                if len(invalid_expanders) == 0:
                    self.log_test_result("Valid Expander Usage", True, 
                                       f"All {len(valid_expanders)} expanders use valid parameters")
                else:
                    self.log_test_result("Valid Expander Usage", False, 
                                       f"Found {len(invalid_expanders)} expanders with invalid parameters")
            else:
                self.log_test_result("Valid Expander Usage", False, "Main app file not found")
                
        except Exception as e:
            self.log_test_result("Valid Expander Usage", False, f"Error: {str(e)}")
    
    def test_button_key_parameters_preserved(self):
        """Test that button key parameters are preserved (they are supported)"""
        logger.info("Testing Button Key Parameters Preserved...")
        
        try:
            main_app_path = Path("app/main.py")
            if main_app_path.exists():
                content = main_app_path.read_text(encoding='utf-8')
                
                # Find button key parameters (these should be preserved)
                button_key_patterns = [
                    'key="refresh_status"',
                    'key="refresh_qr"',
                    'key="retry_login"',
                    'key="sidebar_logout_btn"',
                    'key="refresh_templates_btn"',
                    'key="template_manager_btn"'
                ]
                
                found_keys = []
                for pattern in button_key_patterns:
                    if pattern in content:
                        found_keys.append(pattern)
                
                if len(found_keys) >= 5:  # At least 5 button keys should be present
                    self.log_test_result("Button Key Parameters Preserved", True, 
                                       f"Found {len(found_keys)} button key parameters (preserved)")
                else:
                    self.log_test_result("Button Key Parameters Preserved", False, 
                                       f"Only found {len(found_keys)} button key parameters")
            else:
                self.log_test_result("Button Key Parameters Preserved", False, "Main app file not found")
                
        except Exception as e:
            self.log_test_result("Button Key Parameters Preserved", False, f"Error: {str(e)}")
    
    def test_form_parameters_valid(self):
        """Test that form parameters are valid"""
        logger.info("Testing Form Parameters Valid...")
        
        try:
            main_app_path = Path("app/main.py")
            if main_app_path.exists():
                content = main_app_path.read_text(encoding='utf-8')
                
                # Find all form usages
                form_pattern = r'st\.form\([^)]+\)'
                form_matches = re.findall(form_pattern, content)
                
                valid_forms = []
                for match in form_matches:
                    # Check for valid form parameters
                    if ('clear_on_submit=' in match or 
                        '"login_form"' in match or 
                        '"create_template_form"' in match):
                        valid_forms.append(match)
                
                if len(valid_forms) >= 2:  # Should have at least 2 forms
                    self.log_test_result("Form Parameters Valid", True, 
                                       f"Found {len(valid_forms)} forms with valid parameters")
                else:
                    self.log_test_result("Form Parameters Valid", False, 
                                       f"Only found {len(valid_forms)} valid forms")
            else:
                self.log_test_result("Form Parameters Valid", False, "Main app file not found")
                
        except Exception as e:
            self.log_test_result("Form Parameters Valid", False, f"Error: {str(e)}")
    
    def test_no_unsupported_component_parameters(self):
        """Test that no unsupported parameters are used with any components"""
        logger.info("Testing No Unsupported Component Parameters...")
        
        try:
            main_app_path = Path("app/main.py")
            if main_app_path.exists():
                content = main_app_path.read_text(encoding='utf-8')
                
                # Known unsupported parameter patterns
                unsupported_patterns = [
                    r'st\.expander\([^)]*key=',  # Expander with key
                    r'st\.selectbox\([^)]*disabled=',  # Selectbox with disabled (old versions)
                    r'st\.text_input\([^)]*autocomplete=',  # Text input with autocomplete (old versions)
                ]
                
                found_issues = []
                for pattern in unsupported_patterns:
                    matches = re.findall(pattern, content)
                    if matches:
                        found_issues.extend(matches)
                
                if len(found_issues) == 0:
                    self.log_test_result("No Unsupported Component Parameters", True, 
                                       "No unsupported component parameters found")
                else:
                    self.log_test_result("No Unsupported Component Parameters", False, 
                                       f"Found {len(found_issues)} unsupported parameters")
            else:
                self.log_test_result("No Unsupported Component Parameters", False, "Main app file not found")
                
        except Exception as e:
            self.log_test_result("No Unsupported Component Parameters", False, f"Error: {str(e)}")
    
    def test_crawler_login_expander_functionality(self):
        """Test that crawler login expander functionality is preserved"""
        logger.info("Testing Crawler Login Expander Functionality...")
        
        try:
            main_app_path = Path("app/main.py")
            if main_app_path.exists():
                content = main_app_path.read_text(encoding='utf-8')
                
                # Check for crawler login expander and its functionality
                functionality_checks = [
                    'st.expander("爬虫登录", expanded=True)' in content,
                    '状态显示' in content,
                    '二维码显示区域' in content,
                    '刷新二维码' in content,
                    '重新登录' in content
                ]
                
                passed_checks = sum(functionality_checks)
                
                if passed_checks >= 4:
                    self.log_test_result("Crawler Login Expander Functionality", True, 
                                       f"Crawler login functionality preserved ({passed_checks}/5 checks)")
                else:
                    self.log_test_result("Crawler Login Expander Functionality", False, 
                                       f"Crawler login functionality incomplete ({passed_checks}/5 checks)")
            else:
                self.log_test_result("Crawler Login Expander Functionality", False, "Main app file not found")
                
        except Exception as e:
            self.log_test_result("Crawler Login Expander Functionality", False, f"Error: {str(e)}")
    
    def run_all_tests(self):
        """Run all Streamlit compatibility tests"""
        logger.info("Starting Streamlit Component Compatibility Test Suite...")
        logger.info("=" * 60)
        
        # Run all tests
        self.test_expander_key_parameter_removed()
        self.test_valid_expander_usage()
        self.test_button_key_parameters_preserved()
        self.test_form_parameters_valid()
        self.test_no_unsupported_component_parameters()
        self.test_crawler_login_expander_functionality()
        
        # Print summary
        logger.info("=" * 60)
        logger.info("Test Summary:")
        
        passed = sum(1 for _, result, _ in self.test_results if result)
        total = len(self.test_results)
        
        logger.info(f"Passed: {passed}/{total}")
        
        if passed == total:
            logger.info("🎉 All tests passed! Streamlit component compatibility is working correctly.")
        else:
            logger.warning(f"⚠️ {total - passed} tests failed. Please review the issues above.")
            
        return passed == total


def main():
    """Main test function"""
    test_suite = StreamlitCompatibilityTest()
    success = test_suite.run_all_tests()
    
    if success:
        print("\n✅ Streamlit compatibility test completed successfully!")
        return 0
    else:
        print("\n❌ Streamlit compatibility test failed!")
        return 1


if __name__ == "__main__":
    try:
        result = main()
        sys.exit(result)
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test failed with error: {str(e)}")
        sys.exit(1)
